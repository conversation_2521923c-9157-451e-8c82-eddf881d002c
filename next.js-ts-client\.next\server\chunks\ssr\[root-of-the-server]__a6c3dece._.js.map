{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/providers/react-query-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\nimport { useState } from 'react'\n\nexport default function ReactQueryProvider({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const [queryClient] = useState(() => new QueryClient())\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools />\n    </QueryClientProvider>\n  )\n}"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMe,SAAS,mBAAmB,EACzC,QAAQ,EAGT;IACC,MAAM,CAAC,YAAY,GAAG,IAAA,iNAAQ,EAAC,IAAM,IAAI,4LAAW;IAEpD,qBACE,8OAAC,6MAAmB;QAAC,QAAQ;;YAC1B;0BACD,8OAAC,0MAAkB;;;;;;;;;;;AAGzB", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/stores/auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { devtools, persist } from 'zustand/middleware'\r\n\r\n\r\ninterface authStoreShape {\r\n  isLoggedIn: boolean\r\n  username: string | null\r\n  regInitiated: boolean\r\n  verificationCodeSubmitted: boolean\r\n  passwordSubmitted: boolean\r\n  customerDetailsSubmitted: boolean\r\n  updateAuthInfoSubmitted: boolean\r\n  // altUsername: string | null\r\n  authInfoVerifyCodeSubmitted: boolean\r\n  setIsLoggedIn: (isLoggedIn: boolean) => void\r\n  // logout: () => void\r\n  setUsername: (username: string | null) => void\r\n  setRegInitiated: (regInitiated: boolean) => void\r\n  setPasswordSubmitted: (passwordSubmitted: boolean) => void\r\n  setVerificationCodeSubmitted: (verificationCodeSubmitted: boolean) => void\r\n  setCustomerDetailsSubmitted: (customerDetailsSubmitted: boolean) => void\r\n  setUpdateAuthInfoSubmitted: (updateAuthInfoSubmitted: boolean) => void\r\n  // setAltUsername: (altUsername: string | null) => void\r\n  setAuthInfoVerifyCodeSubmitted: (authInfoVerifyCodeSubmitted: boolean) => void\r\n}\r\n\r\nconst authStore = create<authStoreShape>()(\r\n  devtools(\r\n    persist(\r\n      (set) => ({\r\n        isLoggedIn: false,\r\n        username: null,\r\n        regInitiated: false,\r\n        verificationCodeSubmitted: false,\r\n        passwordSubmitted: false,\r\n        customerDetailsSubmitted: false,\r\n        updateAuthInfoSubmitted: false,\r\n        // altUsername: null,\r\n        authInfoVerifyCodeSubmitted: false,\r\n\r\n        setIsLoggedIn: (isLoggedIn: boolean) => {\r\n          set({ isLoggedIn })\r\n        },\r\n        // logout: () => {\r\n        //   set({ isLoggedIn: false })\r\n        // },\r\n        setUsername: (username: string | null) => {\r\n          set({ username })\r\n        },\r\n        setRegInitiated: (regInitiated: boolean) => {\r\n          set({ regInitiated })\r\n        },\r\n        setVerificationCodeSubmitted: (verificationCodeSubmitted: boolean) => {\r\n          set({ verificationCodeSubmitted })\r\n        },\r\n        setPasswordSubmitted: (passwordSubmitted: boolean) => {\r\n          set({ passwordSubmitted })\r\n        },\r\n        setCustomerDetailsSubmitted: (customerDetailsSubmitted: boolean) => {\r\n          set({ customerDetailsSubmitted })\r\n        },\r\n        setUpdateAuthInfoSubmitted: (updateAuthInfoSubmitted: boolean) => {\r\n          set({ updateAuthInfoSubmitted })\r\n        },\r\n        // setAltUsername: (altUsername: string | null) => {\r\n        //   set({ altUsername })\r\n        // },\r\n        setAuthInfoVerifyCodeSubmitted: (authInfoVerifyCodeSubmitted: boolean) => {\r\n          set({ authInfoVerifyCodeSubmitted })\r\n        }\r\n      }),\r\n      {\r\n        name: 'auth_store', // Name of the persisted store\r\n        partialize: (state) => ({\r\n          isLoggedIn: state.isLoggedIn,\r\n          // username: state.username,\r\n          // regInitiated: state.regInitiated,\r\n        }),\r\n      }\r\n    )\r\n  )\r\n)\r\n\r\nexport default authStore\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAyBA,MAAM,YAAY,IAAA,kJAAM,IACtB,IAAA,yJAAQ,EACN,IAAA,wJAAO,EACL,CAAC,MAAQ,CAAC;QACR,YAAY;QACZ,UAAU;QACV,cAAc;QACd,2BAA2B;QAC3B,mBAAmB;QACnB,0BAA0B;QAC1B,yBAAyB;QACzB,qBAAqB;QACrB,6BAA6B;QAE7B,eAAe,CAAC;YACd,IAAI;gBAAE;YAAW;QACnB;QACA,kBAAkB;QAClB,+BAA+B;QAC/B,KAAK;QACL,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;QACjB;QACA,iBAAiB,CAAC;YAChB,IAAI;gBAAE;YAAa;QACrB;QACA,8BAA8B,CAAC;YAC7B,IAAI;gBAAE;YAA0B;QAClC;QACA,sBAAsB,CAAC;YACrB,IAAI;gBAAE;YAAkB;QAC1B;QACA,6BAA6B,CAAC;YAC5B,IAAI;gBAAE;YAAyB;QACjC;QACA,4BAA4B,CAAC;YAC3B,IAAI;gBAAE;YAAwB;QAChC;QACA,oDAAoD;QACpD,yBAAyB;QACzB,KAAK;QACL,gCAAgC,CAAC;YAC/B,IAAI;gBAAE;YAA4B;QACpC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,YAAY,MAAM,UAAU;QAG9B,CAAC;AACH;uCAKS", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/lib/api-client.ts"], "sourcesContent": ["import axios, { AxiosError, AxiosRequestConfig } from \"axios\"\r\nimport { FetchPaginatedResponse } from \"../types/types\"\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL,\r\n  withCredentials: true, // Include credentials (cookies) with requests\r\n})\r\n\r\nclass APIClient<TResponse, TRequest = TResponse> {\r\n  endpoint: string\r\n\r\n  constructor(endpoint: string) {\r\n    this.endpoint = endpoint\r\n  }\r\n\r\n  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {\r\n    try {\r\n      const response = await axiosInstance.get<TResponse>(this.endpoint, config)\r\n      return response.data\r\n    } catch (error) {\r\n      this.handleError(error)\r\n      // Add a return statement to ensure a return value in case of an error\r\n      throw new Error((error as AxiosError).message)\r\n    }\r\n  }\r\n\r\n  // This is for paginated responses from the server\r\n  getAll = async (config?: AxiosRequestConfig): Promise<FetchPaginatedResponse<TResponse>> => {\r\n    try {\r\n      const response = await axiosInstance.get<FetchPaginatedResponse<TResponse>>(this.endpoint, config)\r\n      console.log(`${response.config.url}`)\r\n      return response.data\r\n    } catch (error) {\r\n      this.handleError(error)\r\n      // Add a return statement to ensure a return value in case of an error\r\n      throw new Error((error as AxiosError).message)\r\n    }\r\n  }\r\n\r\n  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {\r\n    try {\r\n      const response = await axiosInstance.post<TResponse>(this.endpoint, data, config)\r\n      return response.data\r\n    } catch (error) {\r\n      this.handleError(error)\r\n      // Add a return statement to ensure a return value in case of an error\r\n      throw new Error((error as AxiosError).message)\r\n    }\r\n  }\r\n\r\n  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {\r\n    try {\r\n      const response = await axiosInstance.patch<TResponse>(this.endpoint, data, config)\r\n      return response.data\r\n    } catch (error) {\r\n      this.handleError(error)\r\n      throw new Error((error as AxiosError).message)\r\n    }\r\n  }\r\n\r\n  delete = async (itemId?: number): Promise<TResponse> => {\r\n    try {\r\n      const response = await axiosInstance.delete(`${this.endpoint}/${itemId}/`)\r\n      return response.data\r\n    } catch (error) {\r\n      this.handleError(error)\r\n      // Add a return statement to ensure a return value in case of an error\r\n      throw new Error((error as AxiosError).message)\r\n    }\r\n  }\r\n\r\n  private handleError = (error: unknown): void => {\r\n    if (axios.isAxiosError(error)) {\r\n      // ✅ Don't log cancelled requests as errors\r\n      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {\r\n        // This is a cancelled request, don't log it as an error\r\n        throw error\r\n      }\r\n\r\n      // Don't log expected authentication errors to reduce console noise\r\n      const isAuthError = error.response?.status === 401 || error.response?.status === 403\r\n\r\n      if (!isAuthError) {\r\n        console.error(\"Error message: \", error.message)\r\n        if (error.response) {\r\n          console.error(\"Response data: \", error.response.data)\r\n          console.error(\"Response status: \", error.response.status)\r\n        } else if (error.request) {\r\n          console.error(\"Request data: \", error.request)\r\n        } else {\r\n          console.error(\"Error config: \", error.config)\r\n        }\r\n      }\r\n\r\n      throw {\r\n        message: error.message,\r\n        response: error.response,\r\n      }\r\n    } else {\r\n      console.error(\"Error: \", error)\r\n    }\r\n    throw error\r\n  }\r\n}\r\n\r\nexport default APIClient\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,gBAAgB,gJAAK,CAAC,MAAM,CAAC;IACjC,OAAO;IACP,iBAAiB;AACnB;AAEA,MAAM;IACJ,SAAgB;IAEhB,YAAY,QAAgB,CAAE;QAC5B,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,MAAM,OAAO;QACX,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,GAAG,CAAY,IAAI,CAAC,QAAQ,EAAE;YACnE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW,CAAC;YACjB,sEAAsE;YACtE,MAAM,IAAI,MAAM,AAAC,MAAqB,OAAO;QAC/C;IACF,EAAC;IAED,kDAAkD;IAClD,SAAS,OAAO;QACd,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,GAAG,CAAoC,IAAI,CAAC,QAAQ,EAAE;YAC3F,QAAQ,GAAG,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG,EAAE;YACpC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW,CAAC;YACjB,sEAAsE;YACtE,MAAM,IAAI,MAAM,AAAC,MAAqB,OAAO;QAC/C;IACF,EAAC;IAED,OAAO,OAAO,MAAgB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,IAAI,CAAY,IAAI,CAAC,QAAQ,EAAE,MAAM;YAC1E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW,CAAC;YACjB,sEAAsE;YACtE,MAAM,IAAI,MAAM,AAAC,MAAqB,OAAO;QAC/C;IACF,EAAC;IAED,QAAQ,OAAO,MAAyB;QACtC,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,KAAK,CAAY,IAAI,CAAC,QAAQ,EAAE,MAAM;YAC3E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW,CAAC;YACjB,MAAM,IAAI,MAAM,AAAC,MAAqB,OAAO;QAC/C;IACF,EAAC;IAED,SAAS,OAAO;QACd,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACzE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW,CAAC;YACjB,sEAAsE;YACtE,MAAM,IAAI,MAAM,AAAC,MAAqB,OAAO;QAC/C;IACF,EAAC;IAEO,cAAc,CAAC;QACrB,IAAI,gJAAK,CAAC,YAAY,CAAC,QAAQ;YAC7B,2CAA2C;YAC3C,IAAI,MAAM,IAAI,KAAK,kBAAkB,MAAM,OAAO,KAAK,YAAY;gBACjE,wDAAwD;gBACxD,MAAM;YACR;YAEA,mEAAmE;YACnE,MAAM,cAAc,MAAM,QAAQ,EAAE,WAAW,OAAO,MAAM,QAAQ,EAAE,WAAW;YAEjF,IAAI,CAAC,aAAa;gBAChB,QAAQ,KAAK,CAAC,mBAAmB,MAAM,OAAO;gBAC9C,IAAI,MAAM,QAAQ,EAAE;oBAClB,QAAQ,KAAK,CAAC,mBAAmB,MAAM,QAAQ,CAAC,IAAI;oBACpD,QAAQ,KAAK,CAAC,qBAAqB,MAAM,QAAQ,CAAC,MAAM;gBAC1D,OAAO,IAAI,MAAM,OAAO,EAAE;oBACxB,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;gBAC/C,OAAO;oBACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,MAAM;gBAC9C;YACF;YAEA,MAAM;gBACJ,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;YAC1B;QACF,OAAO;YACL,QAAQ,KAAK,CAAC,WAAW;QAC3B;QACA,MAAM;IACR,EAAC;AACH;uCAEe", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/constants/constants.ts"], "sourcesContent": ["// Cart\r\nexport const CACHE_KEY_CART_ITEMS = 'cart_items'\r\nexport const SIMPLE_CART = 'simple_cart'\r\n\r\n// Order\r\nexport const CACHE_KEY_ORDER_ITEMS = 'order_items'\r\nexport const CACHE_KEY_ORDERS = 'orders'\r\nexport const CACHE_KEY_ORDERS_ADMIN = 'orders_admin'\r\n\r\n// Customer\r\nexport const CUSTOMER_DETAILS = 'customer_details'\r\nexport const CUSTOMER_ADDRESSES = 'customer_addresses'\r\n\r\n// Product\r\nexport const CACHE_KEY_PRODUCTS = 'products'\r\n\r\n// Wishlist\r\nexport const WISHLIST_ITEMS = 'wishlist_items'\r\n\r\nexport const ITEMS_PER_PAGE = Number(process.env.NEXT_PUBLIC_ITEMS_PER_PAGE)\r\n"], "names": [], "mappings": "AAAA,OAAO;;;;;;;;;;;;;;;;;;;;;;;AACA,MAAM,uBAAuB;AAC7B,MAAM,cAAc;AAGpB,MAAM,wBAAwB;AAC9B,MAAM,mBAAmB;AACzB,MAAM,yBAAyB;AAG/B,MAAM,mBAAmB;AACzB,MAAM,qBAAqB;AAG3B,MAAM,qBAAqB;AAG3B,MAAM,iBAAiB;AAEvB,MAAM,iBAAiB", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/auth-hooks.ts"], "sourcesContent": ["import { useMutation, useQueryClient } from '@tanstack/react-query'\r\nimport { AxiosError } from 'axios'\r\nimport authStore from '../stores/auth-store'\r\nimport APIClient from '../lib/api-client'\r\nimport { ErrorResponse, InitRegUserShape, VerificationCredentials } from '../types/types'\r\nimport { passwordSchemaFormInputs } from '@/app/(auth)/register/set-password/page'\r\nimport { VerifyContactShape } from '@/app/(auth)/register/update-info/VerifyAuthContact'\r\nimport { loginSchema } from '../schemas/schemas'\r\nimport z from 'zod'\r\nimport { LoginUserShape } from '@/app/(auth)/login/page'\r\nimport { CUSTOMER_DETAILS } from '../constants/constants'\r\nimport { ChangePasswordShape } from '@/app/(auth)/change-password/page'\r\nimport { ResetRequestShape } from '@/app/(auth)/reset-password/PasswordResetReq'\r\nimport { VerifyDetailShape } from '@/app/(auth)/reset-password/PasswordResetVerify'\r\nimport { NewAuthInfoShape } from '@/app/(auth)/change-auth-info/phone/page'\r\n\r\n\r\ntype RegResponseShape = {\r\n  message: string\r\n  username: string\r\n}\r\n\r\ntype UpdateAuthInfoResponseShape = {\r\n  message: string\r\n  username: string\r\n}\r\n\r\nexport interface UpdateAuthInfoInitShape {\r\n  email?: string\r\n  phone_number?: string\r\n}\r\n\r\ntype ResetPasswordResponseShape = {\r\n  message: string\r\n  email_or_phone: string\r\n}\r\n\r\n// Register hooks\r\nexport const useRegister = () => {\r\n  const { setUsername } = authStore()\r\n\r\n  // In AuthClient Response has defined as: Request = Response \r\n  // If request data is different do not forget to specify the types here. \r\n  const apiClient = new APIClient<RegResponseShape, InitRegUserShape>(`/auth/register/initiate/`)\r\n\r\n  const mutation = useMutation<RegResponseShape, AxiosError<ErrorResponse>, InitRegUserShape>({\r\n    mutationFn: (data: InitRegUserShape) => apiClient.post(data),  // Here `data` is of type `RegisterUserShape`\r\n    onSuccess: (data) => {\r\n      console.log(data)\r\n      setUsername(data.username)\r\n    }\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\nexport const useSendVerifyRegCredentials = () => {\r\n  const apiClient = new APIClient('/auth/register/verify/')\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: VerificationCredentials) => apiClient.post(data)\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\nexport const useSetPassword = () => {\r\n\r\n  const apiClient = new APIClient(`/auth/register/set-password/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: passwordSchemaFormInputs) => apiClient.post(data)\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n// Update customer information hooks\r\nexport const useSendUpdateAuthInfo = () => {\r\n  const apiClient = new APIClient<UpdateAuthInfoResponseShape, UpdateAuthInfoInitShape>('/auth/profile/contact/update/')\r\n  // const { setAltUsername } = authStore()\r\n\r\n  const authInfoMutation = useMutation<UpdateAuthInfoResponseShape, AxiosError<ErrorResponse>, UpdateAuthInfoInitShape>({\r\n    mutationFn: (data) => apiClient.patch(data),\r\n    // onSuccess: (data) => {\r\n    //   console.log(data)\r\n    //   setAltUsername(data.username)\r\n    // }\r\n  })\r\n  \r\n  return { authInfoMutation }\r\n}\r\n\r\nexport const useSendVerifyCode = () => {\r\n  const apiClient = new APIClient('/auth/profile/contact/verify/')\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: VerifyContactShape) => apiClient.post(data)\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n\r\n\r\nexport const useLogin = () => {\r\n  const { setIsLoggedIn } = authStore()\r\n  const apiClient = new APIClient(`/auth/login/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: LoginUserShape) => apiClient.post(data),\r\n    onSuccess: () => { // data is the response data\r\n      // const { access } = data.data\r\n      setIsLoggedIn(true)\r\n    }\r\n    // onSettled: (data, error, variables) => {\r\n    //   console.log(data)\r\n    //   console.log(error)\r\n    //   console.log(variables) // variables are user input data\r\n    // }\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\nexport const useLogout = () => {\r\n  const { setIsLoggedIn } = authStore()\r\n  const queryClient = useQueryClient()\r\n\r\n  const apiClient = new APIClient(`/auth/logout/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: () => apiClient.post(),\r\n    onSuccess: () => {\r\n      console.log('Logout was success')\r\n      setIsLoggedIn(false)\r\n      queryClient.invalidateQueries({\r\n        queryKey: [CUSTOMER_DETAILS],\r\n      })\r\n      // queryClient.refetchQueries({\r\n      //   queryKey: [CUSTOMER_DETAILS]\r\n      // })\r\n      queryClient.removeQueries({\r\n        queryKey: [CUSTOMER_DETAILS]\r\n      })\r\n    }\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n// Change Existing Password\r\nexport const useChangePassword = () => {\r\n\r\n  const apiClient = new APIClient(`/auth/password/change/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: ChangePasswordShape) => apiClient.post(data)\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n// Reset Password \r\nexport const useInitResetPassword = () => {\r\n\r\n  const apiClient = new APIClient<ResetPasswordResponseShape, ResetRequestShape>(`/auth/password/reset/request/`)\r\n\r\n  const mutation = useMutation<ResetPasswordResponseShape, AxiosError<ErrorResponse>, ResetRequestShape>({\r\n    mutationFn: (newPassword: ResetRequestShape) => apiClient.post(newPassword),\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\nexport const useResetPasswordConfirm = () => {\r\n\r\n  const apiClient = new APIClient(`/auth/password/reset/confirm/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: VerifyDetailShape) => apiClient.post(data)\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n// Change Auth Info\r\nexport const useChangeAuthInfo = () => {\r\n  const apiClient = new APIClient(`auth/profile/contact/update/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: NewAuthInfoShape) => apiClient.patch(data),\r\n    onSuccess: () => { // data is the response data\r\n      // const { access } = data.data\r\n      // login()\r\n    }\r\n    // onSettled: (data, error, variables) => {\r\n    //   console.log(data)\r\n    //   console.log(error)\r\n    //   console.log(variables) // variables are user input data\r\n    // }\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\nexport const useSendAuthInfoVerifyCode = () => {\r\n  const apiClient = new APIClient('/auth/profile/contact/verify/')\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (data: VerifyContactShape) => apiClient.post(data)\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAEA;AACA;AAOA;;;;;AA4BO,MAAM,cAAc;IACzB,MAAM,EAAE,WAAW,EAAE,GAAG,IAAA,yIAAS;IAEjC,6DAA6D;IAC7D,yEAAyE;IACzE,MAAM,YAAY,IAAI,sIAAS,CAAqC,CAAC,wBAAwB,CAAC;IAE9F,MAAM,WAAW,IAAA,6LAAW,EAAgE;QAC1F,YAAY,CAAC,OAA2B,UAAU,IAAI,CAAC;QACvD,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC;YACZ,YAAY,KAAK,QAAQ;QAC3B;IACF;IAEA,OAAO;QAAE;IAAS;AACpB;AAEO,MAAM,8BAA8B;IACzC,MAAM,YAAY,IAAI,sIAAS,CAAC;IAEhC,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAAkC,UAAU,IAAI,CAAC;IAChE;IAEA,OAAO;QAAE;IAAS;AACpB;AAEO,MAAM,iBAAiB;IAE5B,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,4BAA4B,CAAC;IAE9D,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAAmC,UAAU,IAAI,CAAC;IACjE;IAEA,OAAO;QAAE;IAAS;AACpB;AAGO,MAAM,wBAAwB;IACnC,MAAM,YAAY,IAAI,sIAAS,CAAuD;IACtF,yCAAyC;IAEzC,MAAM,mBAAmB,IAAA,6LAAW,EAAkF;QACpH,YAAY,CAAC,OAAS,UAAU,KAAK,CAAC;IAKxC;IAEA,OAAO;QAAE;IAAiB;AAC5B;AAEO,MAAM,oBAAoB;IAC/B,MAAM,YAAY,IAAI,sIAAS,CAAC;IAEhC,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAA6B,UAAU,IAAI,CAAC;IAC3D;IAEA,OAAO;QAAE;IAAS;AACpB;AAIO,MAAM,WAAW;IACtB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,yIAAS;IACnC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,YAAY,CAAC;IAE9C,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAAyB,UAAU,IAAI,CAAC;QACrD,WAAW;YACT,+BAA+B;YAC/B,cAAc;QAChB;IAMF;IAEA,OAAO;QAAE;IAAS;AACpB;AAEO,MAAM,YAAY;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,yIAAS;IACnC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,aAAa,CAAC;IAE/C,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,IAAM,UAAU,IAAI;QAChC,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ,cAAc;YACd,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,iJAAgB;iBAAC;YAC9B;YACA,+BAA+B;YAC/B,iCAAiC;YACjC,KAAK;YACL,YAAY,aAAa,CAAC;gBACxB,UAAU;oBAAC,iJAAgB;iBAAC;YAC9B;QACF;IACF;IAEA,OAAO;QAAE;IAAS;AACpB;AAGO,MAAM,oBAAoB;IAE/B,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,sBAAsB,CAAC;IAExD,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAA8B,UAAU,IAAI,CAAC;IAC5D;IAEA,OAAO;QAAE;IAAS;AACpB;AAGO,MAAM,uBAAuB;IAElC,MAAM,YAAY,IAAI,sIAAS,CAAgD,CAAC,6BAA6B,CAAC;IAE9G,MAAM,WAAW,IAAA,6LAAW,EAA2E;QACrG,YAAY,CAAC,cAAmC,UAAU,IAAI,CAAC;IACjE;IAEA,OAAO;QAAE;IAAS;AACpB;AAEO,MAAM,0BAA0B;IAErC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,6BAA6B,CAAC;IAE/D,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAA4B,UAAU,IAAI,CAAC;IAC1D;IAEA,OAAO;QAAE;IAAS;AACpB;AAGO,MAAM,oBAAoB;IAC/B,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,4BAA4B,CAAC;IAE9D,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAA2B,UAAU,KAAK,CAAC;QACxD,WAAW;QACT,+BAA+B;QAC/B,UAAU;QACZ;IAMF;IAEA,OAAO;QAAE;IAAS;AACpB;AAEO,MAAM,4BAA4B;IACvC,MAAM,YAAY,IAAI,sIAAS,CAAC;IAEhC,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,OAA6B,UAAU,IAAI,CAAC;IAC3D;IAEA,OAAO;QAAE;IAAS;AACpB", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/stores/cart-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\nimport { createJSONStorage } from 'zustand/middleware'\r\nimport { CartStoreShape } from '../types/store-types'\r\n\r\n// {\r\n//   title: title,\r\n//   product_image: `https://res.cloudinary.com/dev-kani/${item.product_image[0]?.image}`,\r\n//   quantity: 1,\r\n//   price: item.price,\r\n//   sku: item.sku\r\n// }\r\n\r\nconst cartStore = create<CartStoreShape>()(\r\n  persist(\r\n    (set, get) => ({\r\n      cartId: null,\r\n      selectedVariant: null,\r\n      cartItem: {\r\n        id: null,\r\n        product_id: null,\r\n        product_variant: null,\r\n        quantity: null,\r\n        extra_data: {},\r\n      },\r\n      customer: null,\r\n      selectedAddress: null,\r\n      // paymentOptions: [], // unused\r\n      selectedPaymentOption: null,\r\n\r\n      // Selection state (persist-safe)\r\n      selectedItemIds: [],\r\n      selectAllItems: false,\r\n\r\n      setCustomer: (customer) => set({ customer: customer }),\r\n      setSelectedAddress: (address) => set({ selectedAddress: address }),\r\n      // setPaymentOptions: (option) => set({ paymentOptions: option }),\r\n      setSelectedPaymentOption: (paymentOption) =>\r\n        set({ selectedPaymentOption: paymentOption }),\r\n\r\n      setCartId: (newCartId) => set({ cartId: newCartId }),\r\n      // setCartItems: (cartItemData) => set({ selectedVariant: cartItemData }),\r\n\r\n      setCartItemId: (id: number) =>\r\n        set((state) => ({\r\n          cartItem: { ...state.cartItem, id },\r\n        })),\r\n\r\n      // setProductId: (product_id) => set((state) => ({\r\n      //   cartItems: { ...state.cartItems, product_id }\r\n      // })),\r\n      // setProductVariant: (product_variant) => set((state) => ({\r\n      //   cartItems: { ...state.cartItems, product_variant },\r\n      // })),\r\n      setProductVariant: (product_variant) =>\r\n        set({ selectedVariant: product_variant }),\r\n      // setQuantity: (quantity) => set((state) => ({\r\n      //   cartItems: { ...state.cartItems, quantity }\r\n      // })),\r\n      setExtraData: (extra_data) =>\r\n        set((state) => ({\r\n          cartItem: { ...state.cartItem, extra_data },\r\n        })),\r\n\r\n      resetExtraData: () =>\r\n        set((state) => ({\r\n          cartItem: { ...state.cartItem, extra_data: {} },\r\n        })),\r\n\r\n      // Selection methods using serializable array\r\n      toggleItemSelection: (itemId: number) =>\r\n        set((state) => {\r\n          const current = Array.isArray(state.selectedItemIds)\r\n            ? state.selectedItemIds\r\n            : []\r\n          const setCopy = new Set(current)\r\n          if (setCopy.has(itemId)) setCopy.delete(itemId)\r\n          else setCopy.add(itemId)\r\n          return { selectedItemIds: Array.from(setCopy) }\r\n        }),\r\n\r\n      toggleSelectAll: (allItemIds: number[]) =>\r\n        set((state) => {\r\n          const current = Array.isArray(state.selectedItemIds)\r\n            ? state.selectedItemIds\r\n            : []\r\n          const allSelected = allItemIds.every((id) => current.includes(id))\r\n          if (allSelected) {\r\n            // Deselect all\r\n            return {\r\n              selectedItemIds: [],\r\n              selectAllItems: false,\r\n            }\r\n          } else {\r\n            // Select all\r\n            return {\r\n              selectedItemIds: allItemIds,\r\n              selectAllItems: true,\r\n            }\r\n          }\r\n        }),\r\n\r\n      clearSelection: () =>\r\n        set({\r\n          selectedItemIds: [],\r\n          selectAllItems: false,\r\n        }),\r\n\r\n      setSelectedItems: (itemIds: number[]) =>\r\n        set({\r\n          selectedItemIds: itemIds,\r\n        }),\r\n\r\n      isItemSelected: (itemId: number) => {\r\n        const state = get()\r\n        return Array.isArray(state.selectedItemIds)\r\n          ? state.selectedItemIds.includes(itemId)\r\n          : false\r\n      },\r\n    }),\r\n    {\r\n      name: 'cart_store',\r\n      storage: createJSONStorage(() => localStorage),\r\n      version: 1,\r\n      migrate: (persistedState: unknown, _ver: number) => {\r\n        // reference param to satisfy linter\r\n        void _ver\r\n        // Migrate old Set-based `selectedCartItems` if present\r\n        if (!persistedState || typeof persistedState !== 'object')\r\n          return persistedState\r\n\r\n        const ps = persistedState as Record<string, unknown>\r\n        if (ps.selectedCartItems) {\r\n          const old = ps.selectedCartItems\r\n          let selectedItemIds: number[]\r\n          if (Array.isArray(old)) selectedItemIds = old as number[]\r\n          else if (\r\n            typeof old === 'object' &&\r\n            old !== null &&\r\n            Array.isArray((old as Record<string, unknown>).data)\r\n          ) {\r\n            selectedItemIds = (\r\n              (old as Record<string, unknown>).data as unknown[]\r\n            ).map((v) => Number(v))\r\n          } else if (Symbol.iterator in Object(old)) {\r\n            selectedItemIds = Array.from(old as Iterable<number>)\r\n          } else {\r\n            selectedItemIds = []\r\n          }\r\n          return { ...ps, selectedItemIds }\r\n        }\r\n        return ps\r\n      },\r\n      partialize: (state) => ({\r\n        cartId: state.cartId,\r\n        selectedItemIds: state.selectedItemIds,\r\n      }),\r\n    }\r\n  )\r\n)\r\n\r\nexport default cartStore\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA,IAAI;AACJ,kBAAkB;AAClB,0FAA0F;AAC1F,iBAAiB;AACjB,uBAAuB;AACvB,kBAAkB;AAClB,IAAI;AAEJ,MAAM,YAAY,IAAA,kJAAM,IACtB,IAAA,wJAAO,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,QAAQ;QACR,iBAAiB;QACjB,UAAU;YACR,IAAI;YACJ,YAAY;YACZ,iBAAiB;YACjB,UAAU;YACV,YAAY,CAAC;QACf;QACA,UAAU;QACV,iBAAiB;QACjB,gCAAgC;QAChC,uBAAuB;QAEvB,iCAAiC;QACjC,iBAAiB,EAAE;QACnB,gBAAgB;QAEhB,aAAa,CAAC,WAAa,IAAI;gBAAE,UAAU;YAAS;QACpD,oBAAoB,CAAC,UAAY,IAAI;gBAAE,iBAAiB;YAAQ;QAChE,kEAAkE;QAClE,0BAA0B,CAAC,gBACzB,IAAI;gBAAE,uBAAuB;YAAc;QAE7C,WAAW,CAAC,YAAc,IAAI;gBAAE,QAAQ;YAAU;QAClD,0EAA0E;QAE1E,eAAe,CAAC,KACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU;wBAAE,GAAG,MAAM,QAAQ;wBAAE;oBAAG;gBACpC,CAAC;QAEH,kDAAkD;QAClD,kDAAkD;QAClD,OAAO;QACP,4DAA4D;QAC5D,wDAAwD;QACxD,OAAO;QACP,mBAAmB,CAAC,kBAClB,IAAI;gBAAE,iBAAiB;YAAgB;QACzC,+CAA+C;QAC/C,gDAAgD;QAChD,OAAO;QACP,cAAc,CAAC,aACb,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU;wBAAE,GAAG,MAAM,QAAQ;wBAAE;oBAAW;gBAC5C,CAAC;QAEH,gBAAgB,IACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU;wBAAE,GAAG,MAAM,QAAQ;wBAAE,YAAY,CAAC;oBAAE;gBAChD,CAAC;QAEH,6CAA6C;QAC7C,qBAAqB,CAAC,SACpB,IAAI,CAAC;gBACH,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,eAAe,IAC/C,MAAM,eAAe,GACrB,EAAE;gBACN,MAAM,UAAU,IAAI,IAAI;gBACxB,IAAI,QAAQ,GAAG,CAAC,SAAS,QAAQ,MAAM,CAAC;qBACnC,QAAQ,GAAG,CAAC;gBACjB,OAAO;oBAAE,iBAAiB,MAAM,IAAI,CAAC;gBAAS;YAChD;QAEF,iBAAiB,CAAC,aAChB,IAAI,CAAC;gBACH,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,eAAe,IAC/C,MAAM,eAAe,GACrB,EAAE;gBACN,MAAM,cAAc,WAAW,KAAK,CAAC,CAAC,KAAO,QAAQ,QAAQ,CAAC;gBAC9D,IAAI,aAAa;oBACf,eAAe;oBACf,OAAO;wBACL,iBAAiB,EAAE;wBACnB,gBAAgB;oBAClB;gBACF,OAAO;oBACL,aAAa;oBACb,OAAO;wBACL,iBAAiB;wBACjB,gBAAgB;oBAClB;gBACF;YACF;QAEF,gBAAgB,IACd,IAAI;gBACF,iBAAiB,EAAE;gBACnB,gBAAgB;YAClB;QAEF,kBAAkB,CAAC,UACjB,IAAI;gBACF,iBAAiB;YACnB;QAEF,gBAAgB,CAAC;YACf,MAAM,QAAQ;YACd,OAAO,MAAM,OAAO,CAAC,MAAM,eAAe,IACtC,MAAM,eAAe,CAAC,QAAQ,CAAC,UAC/B;QACN;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,IAAA,kKAAiB,EAAC,IAAM;IACjC,SAAS;IACT,SAAS,CAAC,gBAAyB;QACjC,oCAAoC;QACpC,KAAK;QACL,uDAAuD;QACvD,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAC/C,OAAO;QAET,MAAM,KAAK;QACX,IAAI,GAAG,iBAAiB,EAAE;YACxB,MAAM,MAAM,GAAG,iBAAiB;YAChC,IAAI;YACJ,IAAI,MAAM,OAAO,CAAC,MAAM,kBAAkB;iBACrC,IACH,OAAO,QAAQ,YACf,QAAQ,QACR,MAAM,OAAO,CAAC,AAAC,IAAgC,IAAI,GACnD;gBACA,kBAAkB,AAChB,AAAC,IAAgC,IAAI,CACrC,GAAG,CAAC,CAAC,IAAM,OAAO;YACtB,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAO,MAAM;gBACzC,kBAAkB,MAAM,IAAI,CAAC;YAC/B,OAAO;gBACL,kBAAkB,EAAE;YACtB;YACA,OAAO;gBAAE,GAAG,EAAE;gBAAE;YAAgB;QAClC;QACA,OAAO;IACT;IACA,YAAY,CAAC,QAAU,CAAC;YACtB,QAAQ,MAAM,MAAM;YACpB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;uCAIW", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/cart-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport { AxiosError } from 'axios'\r\nimport APIClient from '../lib/api-client'\r\nimport { CartItemShape, ExtraData } from '../types/store-types'\r\nimport { ProductShape } from '../types/product-types'\r\nimport cartStore from '../stores/cart-store'\r\nimport { CACHE_KEY_CART_ITEMS, SIMPLE_CART } from '../constants/constants'\r\nimport {\r\n  CartShape,\r\n  ErrorResponse,\r\n  ShippingCalculationResponse,\r\n  SimpleCartShape,\r\n} from '../types/types'\r\n\r\ninterface ShippingCalculationReq {\r\n  destination_address_id?: number\r\n  get_all_options?: boolean\r\n}\r\n\r\nexport interface UpdateQtyShape {\r\n  // itemId: number\r\n  quantity: number\r\n}\r\n\r\ninterface CreateCartShape {\r\n  customer?: string | null\r\n}\r\n\r\nconst apiClient = new APIClient<CartResponseShape, CreateCartShape>('/cart/')\r\n\r\ninterface SimpleCartItemShape {\r\n  // cart_id: string\r\n  product_id: number\r\n  // variant_id: number\r\n  quantity: number\r\n  extra_data: ExtraData\r\n  product_variant: number\r\n}\r\n\r\ninterface CartResponseShape {\r\n  id: string\r\n  cart_items: CartItemShape[]\r\n  customer: string | null\r\n  cart_weight: number\r\n  total_price: number\r\n  shipping_cost: number\r\n  grand_total: number\r\n}\r\n\r\nexport const useAddToCart = (product: ProductShape, qty: number) => {\r\n  const queryClient = useQueryClient()\r\n  const { cartId, setCartId, setCartItemId, selectedVariant, cartItem } =\r\n    cartStore()\r\n  const { extra_data } = cartItem\r\n\r\n  const apiClient2 = new APIClient<CartResponseShape, SimpleCartItemShape>(\r\n    `/cart/${cartId}/items/`\r\n  )\r\n\r\n  console.log(cartItem)\r\n  console.log(extra_data)\r\n\r\n  const addToCartMutation = useMutation({\r\n    mutationFn: ({\r\n      product_id,\r\n      product_variant,\r\n      quantity,\r\n      extra_data,\r\n    }: SimpleCartItemShape) =>\r\n      apiClient2.post({\r\n        // cart_id,\r\n        product_id,\r\n        product_variant: product_variant,\r\n        quantity,\r\n        extra_data,\r\n      }),\r\n    onSuccess: (data) => {\r\n      setCartItemId(parseInt(data.id))\r\n      // Only invalidate simple cart cache to avoid triggering expensive useCart queries\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SIMPLE_CART],\r\n      })\r\n    },\r\n    onError: (error) => {\r\n      console.error('Error adding item to cart:', error)\r\n    },\r\n  })\r\n\r\n  const createCartMutation = useMutation<\r\n    CartResponseShape,\r\n    AxiosError<ErrorResponse>,\r\n    CreateCartShape\r\n  >({\r\n    mutationFn: () => apiClient.post({}),\r\n    onSuccess: (data) => {\r\n      if (!selectedVariant) {\r\n        return\r\n      }\r\n      console.log('cart id (res) in createCartMutation', data)\r\n      setCartId(data.id)\r\n      addToCartMutation.mutate({\r\n        // cart_id: data.id,\r\n        product_id: product.id,\r\n        product_variant: selectedVariant.id,\r\n        quantity: qty,\r\n        extra_data: extra_data,\r\n      })\r\n      // Only invalidate simple cart cache to avoid triggering expensive useCart queries\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SIMPLE_CART],\r\n      })\r\n    },\r\n    onError: (error) => {\r\n      console.error('Error creating cart:', error)\r\n    },\r\n  })\r\n\r\n  const handleAddToCart = () => {\r\n    if (!selectedVariant) {\r\n      return // Early return if no variant selected\r\n    }\r\n    if (cartId) {\r\n      addToCartMutation.mutate({\r\n        // cart_id: cartId,\r\n        product_id: product.id,\r\n        product_variant: selectedVariant.id,\r\n        quantity: qty,\r\n        extra_data: extra_data,\r\n      })\r\n    } else {\r\n      createCartMutation.mutate({})\r\n    }\r\n  }\r\n\r\n  return {\r\n    handleAddToCart,\r\n    isPending: addToCartMutation.isPending || createCartMutation.isPending,\r\n    error: addToCartMutation.error || createCartMutation.error,\r\n  }\r\n}\r\n\r\nexport const useCart = () => {\r\n  const { cartId } = cartStore()\r\n\r\n  const apiClient = new APIClient<CartShape>(`/cart/${cartId}`)\r\n\r\n  return useQuery({\r\n    queryKey: [CACHE_KEY_CART_ITEMS, cartId],\r\n    queryFn: apiClient.get,\r\n    enabled: !!cartId, // Only run the query if cartId is truthy\r\n    // keepPreviousData: true,\r\n    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n    // initialData:  Here we can add categories as static data\r\n    // refetchOnMount: true,\r\n    staleTime: 0,\r\n  })\r\n}\r\n\r\nexport const useSimpleCart = () => {\r\n  const { cartId } = cartStore()\r\n\r\n  const apiClient = new APIClient<SimpleCartShape>(`/cart/${cartId}/simple`)\r\n\r\n  return useQuery({\r\n    queryKey: [SIMPLE_CART, cartId],\r\n    queryFn: apiClient.get,\r\n    enabled: !!cartId, // Only run the query if cartId is truthy\r\n    staleTime: 0,\r\n  })\r\n}\r\n\r\nexport const useDeleteCartItem = () => {\r\n  const { cartId } = cartStore()\r\n  const queryClient = useQueryClient()\r\n\r\n  const apiClient = new APIClient(`/cart/${cartId}/items`)\r\n\r\n  return useMutation({\r\n    mutationFn: (itemId: number) => apiClient.delete(itemId),\r\n    // onMutate: async (itemId) => {\r\n    //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)\r\n\r\n    //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)\r\n\r\n    //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {\r\n    //     const updatedCartItems = oldData?.cart_items.filter(\r\n    //       (item) => item.id !== itemId\r\n    //     )\r\n    //     return { ...oldData, cart_items: updatedCartItems }\r\n    //   })\r\n\r\n    //   return { previousCartItems }\r\n    // },\r\n\r\n    onSuccess: () => {\r\n      // Invalidate both regular cart and simple cart queries\r\n      // queryClient.invalidateQueries({\r\n      //   queryKey: [CACHE_KEY_CART_ITEMS],\r\n      // })\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SIMPLE_CART],\r\n      })\r\n    },\r\n\r\n    // onError: (err, itemId, context) => {\r\n    //   if (context?.previousCartItems) {\r\n    //     queryClient.setQueryData(CACHE_KEY_CART_ITEMS, context.previousCartItems)\r\n    //   }\r\n    // },\r\n\r\n    // onSettled: async () => {\r\n    //   return await queryClient.invalidateQueries({\r\n    //     queryKey: CACHE_KEY_CART_ITEMS\r\n    //   })\r\n    // },\r\n\r\n    // onSettled: () => {\r\n    //   queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)\r\n    //   // queryClient.invalidateQueries({ queryKey: CACHE_KEY_CART_ITEMS });\r\n    // },\r\n  })\r\n  // const handleDeleteCartItem = (itemId) => {\r\n  //   mutate({ itemId })\r\n  // }\r\n\r\n  // return { isPending, isError, mutate }\r\n}\r\n\r\nexport const useUpdateCart = () => {\r\n  const { cartId } = cartStore()\r\n  const queryClient = useQueryClient()\r\n\r\n  const handleQuantityUpdate = (itemId: number, newQuantity: number) => {\r\n    const cartItemApiClient = new APIClient<UpdateQtyShape>(\r\n      `/cart/${cartId}/items/${itemId}/`\r\n    ) // Item-specific endpoint\r\n    mutation.mutate({ itemId, newQuantity, apiClient: cartItemApiClient })\r\n  }\r\n\r\n  const mutation = useMutation<\r\n    UpdateQtyShape,\r\n    AxiosError<ErrorResponse>,\r\n    {\r\n      itemId: number\r\n      newQuantity: number\r\n      apiClient: APIClient<UpdateQtyShape>\r\n    }\r\n  >({\r\n    mutationFn: ({ newQuantity, apiClient }) => {\r\n      // Using the specific APIClient instance for this item\r\n      return apiClient.patch({ quantity: newQuantity })\r\n    },\r\n\r\n    onSuccess: () => {\r\n      // Invalidate both regular cart and simple cart queries\r\n      // queryClient.invalidateQueries({\r\n      //   queryKey: [CACHE_KEY_CART_ITEMS],\r\n      // })\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SIMPLE_CART],\r\n      })\r\n    },\r\n  })\r\n\r\n  return { mutation, handleQuantityUpdate }\r\n}\r\n\r\nexport const useUpdateCartCustomer = () => {\r\n  const { cartId } = cartStore()\r\n  const queryClient = useQueryClient()\r\n\r\n  const apiClient = new APIClient<CartShape>(`/cart/${cartId}/`)\r\n\r\n  const mutation = useMutation<CartShape, AxiosError<ErrorResponse>, {}>({\r\n    mutationFn: () => {\r\n      // Send PATCH request to update cart with customer\r\n      return apiClient.patch({})\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate both regular cart and simple cart queries\r\n      // queryClient.invalidateQueries({\r\n      //   queryKey: [CACHE_KEY_CART_ITEMS],\r\n      // })\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SIMPLE_CART],\r\n      })\r\n    },\r\n    onError: (error) => {\r\n      console.error('Error updating cart customer:', error)\r\n    },\r\n  })\r\n\r\n  return {\r\n    updateCartCustomer: mutation.mutate,\r\n    isPending: mutation.isPending,\r\n    error: mutation.error,\r\n    isSuccess: mutation.isSuccess,\r\n  }\r\n}\r\n\r\nexport const useShippingCalculation = () => {\r\n  const { cartId } = cartStore()\r\n  const queryClient = useQueryClient()\r\n\r\n  const apiClient = new APIClient<\r\n    ShippingCalculationResponse,\r\n    ShippingCalculationReq\r\n  >(`/cart/${cartId}/items/calculate_shipping/`)\r\n\r\n  const mutation = useMutation<\r\n    ShippingCalculationResponse,\r\n    AxiosError<ErrorResponse>,\r\n    ShippingCalculationReq\r\n  >({\r\n    mutationFn: (data) => apiClient.post(data),\r\n    onSuccess: () => {\r\n      // Invalidate cart queries to trigger refetch with updated shipping data\r\n      // The backend will now return CartWithShippingSerializer automatically\r\n      queryClient.invalidateQueries({\r\n        queryKey: [CACHE_KEY_CART_ITEMS, cartId],\r\n      })\r\n    },\r\n    onError: (error) => {\r\n      console.error('Error calculating shipping:', error)\r\n    },\r\n  })\r\n\r\n  return {\r\n    calculateShipping: mutation.mutate,\r\n    isPending: mutation.isPending,\r\n    error: mutation.error,\r\n    isSuccess: mutation.isSuccess,\r\n    data: mutation.data,\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA;AAGA;AACA;;;;;AAsBA,MAAM,YAAY,IAAI,sIAAS,CAAqC;AAqB7D,MAAM,eAAe,CAAC,SAAuB;IAClD,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,GACnE,IAAA,yIAAS;IACX,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,MAAM,aAAa,IAAI,sIAAS,CAC9B,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC;IAG1B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,MAAM,oBAAoB,IAAA,6LAAW,EAAC;QACpC,YAAY,CAAC,EACX,UAAU,EACV,eAAe,EACf,QAAQ,EACR,UAAU,EACU,GACpB,WAAW,IAAI,CAAC;gBACd,WAAW;gBACX;gBACA,iBAAiB;gBACjB;gBACA;YACF;QACF,WAAW,CAAC;YACV,cAAc,SAAS,KAAK,EAAE;YAC9B,kFAAkF;YAClF,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,4IAAW;iBAAC;YACzB;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,qBAAqB,IAAA,6LAAW,EAIpC;QACA,YAAY,IAAM,UAAU,IAAI,CAAC,CAAC;QAClC,WAAW,CAAC;YACV,IAAI,CAAC,iBAAiB;gBACpB;YACF;YACA,QAAQ,GAAG,CAAC,uCAAuC;YACnD,UAAU,KAAK,EAAE;YACjB,kBAAkB,MAAM,CAAC;gBACvB,oBAAoB;gBACpB,YAAY,QAAQ,EAAE;gBACtB,iBAAiB,gBAAgB,EAAE;gBACnC,UAAU;gBACV,YAAY;YACd;YACA,kFAAkF;YAClF,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,4IAAW;iBAAC;YACzB;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,iBAAiB;YACpB,QAAO,sCAAsC;QAC/C;QACA,IAAI,QAAQ;YACV,kBAAkB,MAAM,CAAC;gBACvB,mBAAmB;gBACnB,YAAY,QAAQ,EAAE;gBACtB,iBAAiB,gBAAgB,EAAE;gBACnC,UAAU;gBACV,YAAY;YACd;QACF,OAAO;YACL,mBAAmB,MAAM,CAAC,CAAC;QAC7B;IACF;IAEA,OAAO;QACL;QACA,WAAW,kBAAkB,SAAS,IAAI,mBAAmB,SAAS;QACtE,OAAO,kBAAkB,KAAK,IAAI,mBAAmB,KAAK;IAC5D;AACF;AAEO,MAAM,UAAU;IACrB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAE5B,MAAM,YAAY,IAAI,sIAAS,CAAY,CAAC,MAAM,EAAE,QAAQ;IAE5D,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,qJAAoB;YAAE;SAAO;QACxC,SAAS,UAAU,GAAG;QACtB,SAAS,CAAC,CAAC;QACX,0BAA0B;QAC1B,oEAAoE;QACpE,0DAA0D;QAC1D,wBAAwB;QACxB,WAAW;IACb;AACF;AAEO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAE5B,MAAM,YAAY,IAAI,sIAAS,CAAkB,CAAC,MAAM,EAAE,OAAO,OAAO,CAAC;IAEzE,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,4IAAW;YAAE;SAAO;QAC/B,SAAS,UAAU,GAAG;QACtB,SAAS,CAAC,CAAC;QACX,WAAW;IACb;AACF;AAEO,MAAM,oBAAoB;IAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC;IAEvD,OAAO,IAAA,6LAAW,EAAC;QACjB,YAAY,CAAC,SAAmB,UAAU,MAAM,CAAC;QACjD,gCAAgC;QAChC,0DAA0D;QAE1D,6EAA6E;QAE7E,6EAA6E;QAC7E,2DAA2D;QAC3D,qCAAqC;QACrC,QAAQ;QACR,0DAA0D;QAC1D,OAAO;QAEP,iCAAiC;QACjC,KAAK;QAEL,WAAW;YACT,uDAAuD;YACvD,kCAAkC;YAClC,sCAAsC;YACtC,KAAK;YACL,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,4IAAW;iBAAC;YACzB;QACF;IAkBF;AACA,6CAA6C;AAC7C,uBAAuB;AACvB,IAAI;AAEJ,wCAAwC;AAC1C;AAEO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,uBAAuB,CAAC,QAAgB;QAC5C,MAAM,oBAAoB,IAAI,sIAAS,CACrC,CAAC,MAAM,EAAE,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC,EAClC,yBAAyB;;QAC3B,SAAS,MAAM,CAAC;YAAE;YAAQ;YAAa,WAAW;QAAkB;IACtE;IAEA,MAAM,WAAW,IAAA,6LAAW,EAQ1B;QACA,YAAY,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE;YACrC,sDAAsD;YACtD,OAAO,UAAU,KAAK,CAAC;gBAAE,UAAU;YAAY;QACjD;QAEA,WAAW;YACT,uDAAuD;YACvD,kCAAkC;YAClC,sCAAsC;YACtC,KAAK;YACL,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,4IAAW;iBAAC;YACzB;QACF;IACF;IAEA,OAAO;QAAE;QAAU;IAAqB;AAC1C;AAEO,MAAM,wBAAwB;IACnC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,YAAY,IAAI,sIAAS,CAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE7D,MAAM,WAAW,IAAA,6LAAW,EAA2C;QACrE,YAAY;YACV,kDAAkD;YAClD,OAAO,UAAU,KAAK,CAAC,CAAC;QAC1B;QACA,WAAW;YACT,uDAAuD;YACvD,kCAAkC;YAClC,sCAAsC;YACtC,KAAK;YACL,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,4IAAW;iBAAC;YACzB;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,OAAO;QACL,oBAAoB,SAAS,MAAM;QACnC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,WAAW,SAAS,SAAS;IAC/B;AACF;AAEO,MAAM,yBAAyB;IACpC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,YAAY,IAAI,sIAAS,CAG7B,CAAC,MAAM,EAAE,OAAO,0BAA0B,CAAC;IAE7C,MAAM,WAAW,IAAA,6LAAW,EAI1B;QACA,YAAY,CAAC,OAAS,UAAU,IAAI,CAAC;QACrC,WAAW;YACT,wEAAwE;YACxE,uEAAuE;YACvE,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,qJAAoB;oBAAE;iBAAO;YAC1C;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,OAAO;QACL,mBAAmB,SAAS,MAAM;QAClC,WAAW,SAAS,SAAS;QAC7B,OAAO,SAAS,KAAK;QACrB,WAAW,SAAS,SAAS;QAC7B,MAAM,SAAS,IAAI;IACrB;AACF", "debugId": null}}, {"offset": {"line": 939, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/customer-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport { CUSTOMER_DETAILS } from \"../constants/constants\"\r\nimport APIClient from \"../lib/api-client\"\r\nimport { CustomerShape } from \"../types/store-types\"\r\nimport { UpdateCustomerShape } from '@/app/(auth)/register/create-customer/page'\r\n\r\n\r\nexport const useCustomerDetails = (enabled: boolean = true) => {\r\n\r\n  const apiClient = new APIClient<CustomerShape>('/customers/me/')\r\n\r\n  return useQuery({\r\n    queryKey: [CUSTOMER_DETAILS],\r\n    queryFn: () => apiClient.get(),\r\n    enabled,\r\n    // keepPreviousData: true,\r\n    // refetchOnWindowFocus: true,\r\n    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n    // initialData:  Here we can add categories as static data\r\n    // refetchOnMount: true,\r\n  })\r\n}\r\n\r\nexport const useUpdateCustomer = () => {\r\n  const queryClient = useQueryClient()\r\n\r\n  const apiClient = new APIClient(`/customers/me/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (customerData: Partial<UpdateCustomerShape>) => apiClient.patch(customerData),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [CUSTOMER_DETAILS]\r\n      })\r\n    }\r\n  })\r\n\r\n  return { mutation }\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AACA;AACA;;;;AAKO,MAAM,qBAAqB,CAAC,UAAmB,IAAI;IAExD,MAAM,YAAY,IAAI,sIAAS,CAAgB;IAE/C,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,iJAAgB;SAAC;QAC5B,SAAS,IAAM,UAAU,GAAG;QAC5B;IAMF;AACF;AAEO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,cAAc,CAAC;IAEhD,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,eAA+C,UAAU,KAAK,CAAC;QAC5E,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,iJAAgB;iBAAC;YAC9B;QACF;IACF;IAEA,OAAO;QAAE;IAAS;AACpB", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/wishlist-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport { ProductImageShape } from '../types/product-types'\r\nimport APIClient from '../lib/api-client'\r\nimport { WISHLIST_ITEMS } from '../constants/constants'\r\n\r\n\r\ninterface WishlistShape {\r\n  id: number\r\n  product: {\r\n    id: number\r\n    title: string\r\n    slug: string\r\n    is_active: boolean\r\n    average_rating: number\r\n    product_variant: [\r\n      {\r\n        id: number\r\n        price: number\r\n        product_image: ProductImageShape[]\r\n      }\r\n    ]\r\n  }\r\n  added_at: string\r\n}\r\n\r\nexport const useWishlist = (page: number, enabled: boolean = true) => {\r\n\r\n  const apiClient = new APIClient<WishlistShape>(`/wishlist/`)\r\n\r\n  return useQuery({\r\n    queryKey: [WISHLIST_ITEMS, page],\r\n    queryFn: () => apiClient.getAll({\r\n      params: {\r\n        page: page\r\n      }\r\n    }),\r\n    enabled, // Only run the query if enabled is true (i.e., user is authenticated)\r\n    // keepPreviousData: true,\r\n    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n    // initialData:  Here we can add categories as static data\r\n    // refetchOnMount: true,\r\n    staleTime: 0,\r\n\r\n  })\r\n}\r\n\r\nexport const useToggleWishlist = () => {\r\n  const queryClient = useQueryClient()\r\n\r\n  const apiClient = new APIClient(`/wishlist/toggle_product/`)\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (productId: number) => apiClient.post({\r\n      \"product_id\": `${productId}`\r\n    }),\r\n    // onMutate: async (itemId) => {\r\n    //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)\r\n\r\n    //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)\r\n\r\n    //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {\r\n    //     const updatedCartItems = oldData?.cart_items.filter(\r\n    //       (item) => item.id !== itemId\r\n    //     )\r\n    //     return { ...oldData, cart_items: updatedCartItems }\r\n    //   })\r\n\r\n    //   return { previousCartItems }\r\n    // },\r\n\r\n    onSuccess: () => {\r\n      // queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)\r\n      queryClient.invalidateQueries({\r\n        queryKey: [WISHLIST_ITEMS]\r\n      })\r\n    },\r\n\r\n    // onError: (err, itemId, context) => {\r\n    //   if (context?.previousCartItems) {\r\n    //     queryClient.setQueryData(CACHE_KEY_CART_ITEMS, context.previousCartItems)\r\n    //   }\r\n    // },\r\n\r\n    // onSettled: async () => {\r\n    //   return await queryClient.invalidateQueries({\r\n    //     queryKey: CACHE_KEY_CART_ITEMS\r\n    //   })\r\n    // },\r\n\r\n    // onSettled: () => {\r\n    //   queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)\r\n    //   // queryClient.invalidateQueries({ queryKey: CACHE_KEY_CART_ITEMS });\r\n    // },\r\n\r\n  })\r\n\r\n  // const handleDeleteCartItem = (itemId) => {\r\n  //   mutate({ itemId })\r\n  // }\r\n\r\n  // return { isPending, isError, mutate }\r\n\r\n  return mutation\r\n\r\n}\r\n\r\nexport const useDeleteWishlistItem = () => {\r\n  const queryClient = useQueryClient()\r\n  const apiClient = new APIClient('/wishlist')\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: (itemId: number) => {\r\n      // This will correctly construct the URL as /wishlist/123/\r\n      return apiClient.delete(itemId)\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate the wishlist query to refetch the latest data\r\n      queryClient.invalidateQueries({\r\n        queryKey: [WISHLIST_ITEMS]\r\n      })\r\n    },\r\n    onError: (error) => {\r\n      // Optional: Add error handling\r\n      console.error('Failed to delete wishlist item:', error)\r\n    }\r\n  })\r\n\r\n  return mutation\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAEA;AACA;;;;AAsBO,MAAM,cAAc,CAAC,MAAc,UAAmB,IAAI;IAE/D,MAAM,YAAY,IAAI,sIAAS,CAAgB,CAAC,UAAU,CAAC;IAE3D,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,+IAAc;YAAE;SAAK;QAChC,SAAS,IAAM,UAAU,MAAM,CAAC;gBAC9B,QAAQ;oBACN,MAAM;gBACR;YACF;QACA;QACA,0BAA0B;QAC1B,oEAAoE;QACpE,0DAA0D;QAC1D,wBAAwB;QACxB,WAAW;IAEb;AACF;AAEO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,yBAAyB,CAAC;IAE3D,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC,YAAsB,UAAU,IAAI,CAAC;gBAChD,cAAc,GAAG,WAAW;YAC9B;QACA,gCAAgC;QAChC,0DAA0D;QAE1D,6EAA6E;QAE7E,6EAA6E;QAC7E,2DAA2D;QAC3D,qCAAqC;QACrC,QAAQ;QACR,0DAA0D;QAC1D,OAAO;QAEP,iCAAiC;QACjC,KAAK;QAEL,WAAW;YACT,sDAAsD;YACtD,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,+IAAc;iBAAC;YAC5B;QACF;IAmBF;IAEA,6CAA6C;IAC7C,uBAAuB;IACvB,IAAI;IAEJ,wCAAwC;IAExC,OAAO;AAET;AAEO,MAAM,wBAAwB;IACnC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,YAAY,IAAI,sIAAS,CAAC;IAEhC,MAAM,WAAW,IAAA,6LAAW,EAAC;QAC3B,YAAY,CAAC;YACX,0DAA0D;YAC1D,OAAO,UAAU,MAAM,CAAC;QAC1B;QACA,WAAW;YACT,2DAA2D;YAC3D,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,+IAAc;iBAAC;YAC5B;QACF;QACA,SAAS,CAAC;YACR,+BAA+B;YAC/B,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/logo/Logo.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"logo\": \"Logo-module-scss-module__VZ4g6W__logo\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/logo/Logo.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport styles from './Logo.module.scss'\r\n\r\nconst Logo = () => {\r\n  return (\r\n    <Link\r\n      href='/'\r\n      className={styles.logo}\r\n    ><h1>PC</h1><span>House</span>\r\n    </Link>\r\n  )\r\n}\r\n\r\nexport default Logo"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,OAAO;IACX,qBACE,8OAAC,uKAAI;QACH,MAAK;QACL,WAAW,kKAAM,CAAC,IAAI;;0BACvB,8OAAC;0BAAG;;;;;;0BAAO,8OAAC;0BAAK;;;;;;;;;;;;AAGtB;uCAEe", "debugId": null}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/header/navbar/navigation-card/NavigationCard.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"level-0\": \"NavigationCard-module-scss-module__8oXgDG__level-0\",\n  \"level-1\": \"NavigationCard-module-scss-module__8oXgDG__level-1\",\n  \"level-2\": \"NavigationCard-module-scss-module__8oXgDG__level-2\",\n  \"level-3\": \"NavigationCard-module-scss-module__8oXgDG__level-3\",\n  \"list\": \"NavigationCard-module-scss-module__8oXgDG__list\",\n  \"nav__card\": \"NavigationCard-module-scss-module__8oXgDG__nav__card\",\n  \"slideDown\": \"NavigationCard-module-scss-module__8oXgDG__slideDown\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/alert/Alert.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"alert\": \"Alert-module-scss-module__QYFb8W__alert\",\n  \"error\": \"Alert-module-scss-module__QYFb8W__error\",\n  \"highlight\": \"Alert-module-scss-module__QYFb8W__highlight\",\n  \"info\": \"Alert-module-scss-module__QYFb8W__info\",\n  \"success\": \"Alert-module-scss-module__QYFb8W__success\",\n  \"warning\": \"Alert-module-scss-module__QYFb8W__warning\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/alert/Alert.tsx"], "sourcesContent": ["import styles from './Alert.module.scss'\r\n\r\ninterface Props {\r\n  variant: 'info' | 'warning' | 'error' | 'success'\r\n  message: string\r\n  textSize?: string\r\n  textAlign?: 'left' | 'right' | 'center' | 'justify'\r\n  highlightWords?: string[] // New prop to pass words for highlighting\r\n}\r\n\r\nconst Alert = ({ variant, message, textSize, textAlign = 'left', highlightWords = [] }: Props) => {\r\n\r\n  const escapeRegExp = (string: string) => {\r\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') // Escape special characters\r\n  }\r\n\r\n  const getHighlightedMessage = (text: string) => {\r\n    if (!highlightWords.length) {\r\n      return text\r\n    }\r\n\r\n    // Escape each word in the highlightWords array\r\n    const escapedHighlightWords = highlightWords.map(word => escapeRegExp(word))\r\n    const parts = text.split(new RegExp(`(${escapedHighlightWords.join('|')})`, 'gi'))\r\n\r\n    return parts.map((part, index) => (\r\n      highlightWords.some(word => word.toLowerCase() === part.toLowerCase())\r\n        ? <span key={index} className={styles.highlight}>{part}</span>\r\n        : part\r\n    ))\r\n  }\r\n\r\n\r\n  return (\r\n    <div className={`${styles[variant]} ${styles.alert}`}>\r\n      <p style={{\r\n        fontSize: `${textSize}px`,\r\n        textAlign: textAlign\r\n      }}>\r\n        {getHighlightedMessage(message)}\r\n      </p>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Alert\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAUA,MAAM,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,MAAM,EAAE,iBAAiB,EAAE,EAAS;IAE3F,MAAM,eAAe,CAAC;QACpB,OAAO,OAAO,OAAO,CAAC,uBAAuB,QAAQ,4BAA4B;;IACnF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,eAAe,MAAM,EAAE;YAC1B,OAAO;QACT;QAEA,+CAA+C;QAC/C,MAAM,wBAAwB,eAAe,GAAG,CAAC,CAAA,OAAQ,aAAa;QACtE,MAAM,QAAQ,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,sBAAsB,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAE5E,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,QACtB,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW,OAAO,KAAK,WAAW,oBAC/D,8OAAC;gBAAiB,WAAW,oKAAM,CAAC,SAAS;0BAAG;eAArC;;;;2DACX;IAER;IAGA,qBACE,8OAAC;QAAI,WAAW,GAAG,oKAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,oKAAM,CAAC,KAAK,EAAE;kBAClD,cAAA,8OAAC;YAAE,OAAO;gBACR,UAAU,GAAG,SAAS,EAAE,CAAC;gBACzB,WAAW;YACb;sBACG,sBAAsB;;;;;;;;;;;AAI/B;uCAEe", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/getErrorMessage.ts"], "sourcesContent": ["import { AxiosError } from \"axios\"\r\nimport { ErrorResponse } from \"../../types/types\"\r\n\r\nexport const getErrorMessage = (error: AxiosError<ErrorResponse>) => {\r\n  if (error?.response?.data) {\r\n    return Object.values(error.response.data).flat().join(', ')\r\n  }\r\n  return error?.message\r\n}\r\n"], "names": [], "mappings": ";;;;AAGO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,UAAU,MAAM;QACzB,OAAO,OAAO,MAAM,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC;IACxD;IACA,OAAO,OAAO;AAChB", "debugId": null}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/navbar/navigation-card/NavigationCard.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport { Category } from '../../../../types/product-types' // Import existing Category interface\r\nimport styles from './NavigationCard.module.scss'\r\nimport Alert from '../../../utils/alert/Alert'\r\nimport { getErrorMessage } from '../../../utils/getErrorMessage'\r\nimport { AxiosError } from 'axios'\r\nimport { ErrorResponse } from '../../../../types/types'\r\n\r\n\r\ninterface Props {\r\n  isPending: boolean\r\n  error: unknown\r\n  categories: Category[]\r\n  isOpen: boolean\r\n  setIsOpen: (isOpen: boolean) => void\r\n}\r\n\r\n// Utility function to build a hierarchical structure of categories\r\nconst buildHierarchy = (categories: Category[]): Category[] => {\r\n  const categoryMap = new Map<number, Category>() // Map to hold categories by their IDs\r\n\r\n  // Initialize the map with empty children arrays\r\n  categories.forEach(cat =>\r\n    categoryMap.set(cat.id, { ...cat, children: [] })\r\n  )\r\n\r\n  const rootCategories: Category[] = [] // Array to hold top-level categories\r\n\r\n  categories.forEach(cat => {\r\n    if (cat.parent === null) {\r\n      rootCategories.push(categoryMap.get(cat.id)!) // Add top-level categories (parent is null) to rootCategories\r\n    } else {\r\n      const parentCategory = categoryMap.get(cat.parent)\r\n      if (parentCategory) {\r\n        parentCategory.children!.push(categoryMap.get(cat.id)!) // Add the current category to its parent's children array\r\n      }\r\n    }\r\n  })\r\n\r\n  return rootCategories // Return the hierarchical structure\r\n}\r\n\r\n// Main functional component for the navigation card\r\nconst NavigationCard = ({ isPending, error, categories, setIsOpen }: Props) => {\r\n  const hierarchicalCategories = buildHierarchy(categories) // Build hierarchical structure of categories\r\n\r\n  // Recursive function to render categories and their children\r\n  const renderCategories = (items: Category[], level: number = 0) => (\r\n    <ul className={`${styles.list} ${styles[`level-${level}`]}`}>\r\n      {/* Apply level-specific styles using SCSS classes */}\r\n      {items.map(item => ( // Iterate over each category\r\n        <li key={item.id}>\r\n          <Link\r\n            href={`/products/category/${item.slug}`} // Link to the category's page\r\n            onClick={() => setIsOpen(false)} // Close navigation card on link click\r\n          >\r\n            {item.title} {/* Display the category title */}\r\n          </Link>\r\n          {item.children && item.children.length > 0 && renderCategories(item.children, level + 1)}\r\n          {/* Recursively render children categories */}\r\n        </li>\r\n      ))}\r\n    </ul>\r\n  )\r\n\r\n  return (\r\n    <div\r\n      onMouseLeave={() => setIsOpen(false)} // Close navigation card on mouse leave\r\n      onMouseOver={() => setIsOpen(true)} // Keep navigation card open on hover\r\n      className={`${styles.nav__card} container`} // Apply styling classes\r\n    >\r\n      {isPending ? (\r\n        <div>Loading...</div> // Show loading state if data is still fetching\r\n      ) : error ? (\r\n        <Alert variant=\"error\" message={getErrorMessage(error as AxiosError<ErrorResponse>)} />\r\n        // Show an error alert if an error occurs\r\n      ) : (\r\n        renderCategories(hierarchicalCategories)\r\n        // Render the category hierarchy if no errors and not loading\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default NavigationCard\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;AACA;;;;;;AAaA,mEAAmE;AACnE,MAAM,iBAAiB,CAAC;IACtB,MAAM,cAAc,IAAI,MAAwB,sCAAsC;;IAEtF,gDAAgD;IAChD,WAAW,OAAO,CAAC,CAAA,MACjB,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAAE,GAAG,GAAG;YAAE,UAAU,EAAE;QAAC;IAGjD,MAAM,iBAA6B,EAAE,CAAC,qCAAqC;;IAE3E,WAAW,OAAO,CAAC,CAAA;QACjB,IAAI,IAAI,MAAM,KAAK,MAAM;YACvB,eAAe,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,8DAA8D;QAC9G,OAAO;YACL,MAAM,iBAAiB,YAAY,GAAG,CAAC,IAAI,MAAM;YACjD,IAAI,gBAAgB;gBAClB,eAAe,QAAQ,CAAE,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,0DAA0D;YACpH;QACF;IACF;IAEA,OAAO,eAAe,oCAAoC;;AAC5D;AAEA,oDAAoD;AACpD,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAS;IACxE,MAAM,yBAAyB,eAAe,YAAY,6CAA6C;;IAEvG,6DAA6D;IAC7D,MAAM,mBAAmB,CAAC,OAAmB,QAAgB,CAAC,iBAC5D,8OAAC;YAAG,WAAW,GAAG,qMAAM,CAAC,IAAI,CAAC,CAAC,EAAE,qMAAM,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE;sBAExD,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC;;sCACC,8OAAC,uKAAI;4BACH,MAAM,CAAC,mBAAmB,EAAE,KAAK,IAAI,EAAE;4BACvC,SAAS,IAAM,UAAU;;gCAExB,KAAK,KAAK;gCAAC;;;;;;;wBAEb,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,iBAAiB,KAAK,QAAQ,EAAE,QAAQ;;mBAP/E,KAAK,EAAE;;;;;;;;;;IActB,qBACE,8OAAC;QACC,cAAc,IAAM,UAAU;QAC9B,aAAa,IAAM,UAAU;QAC7B,WAAW,GAAG,qMAAM,CAAC,SAAS,CAAC,UAAU,CAAC;kBAEzC,0BACC,8OAAC;sBAAI;;;;;uDACH,sBACF,8OAAC,wJAAK;YAAC,SAAQ;YAAQ,SAAS,IAAA,gKAAe,EAAC;;;;;uDAGhD,iBAAiB;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/stores/filter-store.ts"], "sourcesContent": ["// Zustand store (filterStore.ts)\r\nimport { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface FilterStoreShape {\r\n  productTypeId: number\r\n  currentCategory: string\r\n  categoryProductTypeMap: Record<string, number>\r\n  selectedFilters: Record<string, string | number | (string | number)[] | [number, number]>\r\n  setProductTypeId: (proType: number) => void\r\n  setCurrentCategory: (category: string) => void\r\n  setCategoryProductType: (category: string, productTypeId: number) => void\r\n  updateFilter: (filterName: string, filterValue: string | number | (string | number)[]) => void\r\n  resetFilters: () => void\r\n  shouldFetchFilters: (category: string) => boolean\r\n}\r\n\r\nconst filterStore = create<FilterStoreShape>()(\r\n  persist(\r\n    (set, get) => ({\r\n  productTypeId: 0,\r\n  currentCategory: '',\r\n  categoryProductTypeMap: {},\r\n  selectedFilters: {},\r\n\r\n  setProductTypeId: (proType) => set({ productTypeId: proType }),\r\n\r\n  setCurrentCategory: (category) => {\r\n    const state = get()\r\n    if (state.currentCategory !== category) {\r\n      // Category changed, reset filters and update current category\r\n      const cachedProductTypeId = state.categoryProductTypeMap[category]\r\n      set({\r\n        currentCategory: category,\r\n        selectedFilters: {},\r\n        // Only set productTypeId if we have cached data, otherwise keep current value\r\n        ...(cachedProductTypeId && { productTypeId: cachedProductTypeId })\r\n      })\r\n    }\r\n  },\r\n\r\n  setCategoryProductType: (category, productTypeId) => {\r\n    const state = get()\r\n    // Only update if the productTypeId is different to avoid unnecessary re-renders\r\n    if (state.categoryProductTypeMap[category] !== productTypeId || state.productTypeId !== productTypeId) {\r\n      set({\r\n        categoryProductTypeMap: {\r\n          ...state.categoryProductTypeMap,\r\n          [category]: productTypeId\r\n        },\r\n        productTypeId: productTypeId\r\n      })\r\n    }\r\n  },\r\n\r\n  updateFilter: (filterName, filterValue) =>\r\n    set((state) => ({\r\n      selectedFilters: { ...state.selectedFilters, [filterName]: filterValue },\r\n    })),\r\n\r\n  resetFilters: () => set({ selectedFilters: {} }),\r\n\r\n  shouldFetchFilters: (category) => {\r\n    const state = get()\r\n    // Only fetch filters if we don't have cached productTypeId for this category\r\n    return !state.categoryProductTypeMap[category]\r\n  },\r\n    }),\r\n    {\r\n      name: 'filter-store', // unique name for localStorage key\r\n      partialize: (state) => ({\r\n        categoryProductTypeMap: state.categoryProductTypeMap\r\n      }), // only persist the category-product type mapping\r\n    }\r\n  )\r\n)\r\n\r\nexport default filterStore\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AACjC;AACA;;;AAeA,MAAM,cAAc,IAAA,kJAAM,IACxB,IAAA,wJAAO,EACL,CAAC,KAAK,MAAQ,CAAC;QACjB,eAAe;QACf,iBAAiB;QACjB,wBAAwB,CAAC;QACzB,iBAAiB,CAAC;QAElB,kBAAkB,CAAC,UAAY,IAAI;gBAAE,eAAe;YAAQ;QAE5D,oBAAoB,CAAC;YACnB,MAAM,QAAQ;YACd,IAAI,MAAM,eAAe,KAAK,UAAU;gBACtC,8DAA8D;gBAC9D,MAAM,sBAAsB,MAAM,sBAAsB,CAAC,SAAS;gBAClE,IAAI;oBACF,iBAAiB;oBACjB,iBAAiB,CAAC;oBAClB,8EAA8E;oBAC9E,GAAI,uBAAuB;wBAAE,eAAe;oBAAoB,CAAC;gBACnE;YACF;QACF;QAEA,wBAAwB,CAAC,UAAU;YACjC,MAAM,QAAQ;YACd,gFAAgF;YAChF,IAAI,MAAM,sBAAsB,CAAC,SAAS,KAAK,iBAAiB,MAAM,aAAa,KAAK,eAAe;gBACrG,IAAI;oBACF,wBAAwB;wBACtB,GAAG,MAAM,sBAAsB;wBAC/B,CAAC,SAAS,EAAE;oBACd;oBACA,eAAe;gBACjB;YACF;QACF;QAEA,cAAc,CAAC,YAAY,cACzB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB;wBAAE,GAAG,MAAM,eAAe;wBAAE,CAAC,WAAW,EAAE;oBAAY;gBACzE,CAAC;QAEH,cAAc,IAAM,IAAI;gBAAE,iBAAiB,CAAC;YAAE;QAE9C,oBAAoB,CAAC;YACnB,MAAM,QAAQ;YACd,6EAA6E;YAC7E,OAAO,CAAC,MAAM,sBAAsB,CAAC,SAAS;QAChD;IACE,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,wBAAwB,MAAM,sBAAsB;QACtD,CAAC;AACH;uCAIW", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/product-hooks.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\"\r\nimport APIClient from \"../lib/api-client\"\r\nimport { Category, ProductShape } from \"../types/product-types\"\r\nimport { CACHE_KEY_PRODUCTS } from \"../constants/constants\"\r\nimport filterStore from \"../stores/filter-store\"\r\nimport { FilterOptionsShape } from \"../types/types\"\r\n\r\n\r\nconst apiClient = new APIClient<Category[]>('/products/categories/')\r\n\r\nexport const useCategories = () => useQuery({\r\n  queryKey: ['categories'],\r\n  queryFn: ({ signal }) => apiClient.get({ signal }), // ✅ Pass abort signal\r\n  staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n  // initialData:  Here we can add categories as static data\r\n  // cacheTime: 1000 * 60 * 60, <-- This is wrong!\r\n  gcTime: 1000 * 60 * 60, // 1 hour\r\n  retry: (failureCount, error) => {\r\n    // ✅ Don't retry on cancelled requests\r\n    if (error.name === 'CanceledError' || error.message === 'canceled') {\r\n      return false\r\n    }\r\n    return failureCount < 3\r\n  }\r\n})\r\n\r\nexport const useProducts = (slug: string, page: number, queryString: string, searchQuery: string = '') => {\r\n  const { selectedFilters } = filterStore()\r\n\r\n  // Initialize API client based on whether searchQuery exists or not\r\n  const apiClient = searchQuery\r\n    ? new APIClient<ProductShape>(`/products/?search=${searchQuery}`)\r\n    : new APIClient<ProductShape>(`/products/category/${slug}/`)\r\n\r\n  const queryParams = new URLSearchParams(queryString)\r\n\r\n  // Add filters to the query parameters\r\n  Object.entries(selectedFilters).forEach(([key, value]) => {\r\n    if (key === 'price_range' && Array.isArray(value)) {\r\n      queryParams.set('min_price', value[0].toString())\r\n      queryParams.set('max_price', value[1].toString())\r\n    } else if (Array.isArray(value)) {\r\n      value.forEach((val) => queryParams.append(key, val.toString()))\r\n    } else {\r\n      queryParams.set(key, value.toString())\r\n    }\r\n  })\r\n\r\n  // If searchQuery exists, add it to queryParams\r\n  if (searchQuery) {\r\n    queryParams.set('search', searchQuery)\r\n  }\r\n\r\n  return useQuery({\r\n    queryKey: [CACHE_KEY_PRODUCTS, slug, selectedFilters, page, queryString, searchQuery],\r\n    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),\r\n  })\r\n}\r\n\r\nexport const useProductFilterOptions = (productTypeId: number) => {\r\n  const apiClient = new APIClient<FilterOptionsShape>('/products/product-filter-options/')\r\n\r\n  return useQuery({\r\n    queryKey: ['filterOptions', productTypeId],\r\n    queryFn: () => apiClient.get({\r\n      params: { product_type_id: productTypeId }\r\n    }),\r\n    enabled: !!productTypeId && productTypeId > 0, // Only fetch if productTypeId is valid\r\n    staleTime: 1000 * 60 * 60 * 24, // 24 hours - filters don't change often\r\n    gcTime: 1000 * 60 * 60 * 24, // 24 hours - keep in cache longer\r\n    refetchOnWindowFocus: false, // Don't refetch on window focus\r\n    refetchOnMount: false, // Don't refetch on component mount if data exists\r\n    refetchOnReconnect: false, // Don't refetch on network reconnect\r\n    retry: 1, // Only retry once on failure\r\n  })\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAEA;AACA;;;;;AAIA,MAAM,YAAY,IAAI,sIAAS,CAAa;AAErC,MAAM,gBAAgB,IAAM,IAAA,uLAAQ,EAAC;QAC1C,UAAU;YAAC;SAAa;QACxB,SAAS,CAAC,EAAE,MAAM,EAAE,GAAK,UAAU,GAAG,CAAC;gBAAE;YAAO;QAChD,WAAW,KAAK,KAAK,KAAK;QAC1B,0DAA0D;QAC1D,gDAAgD;QAChD,QAAQ,OAAO,KAAK;QACpB,OAAO,CAAC,cAAc;YACpB,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,mBAAmB,MAAM,OAAO,KAAK,YAAY;gBAClE,OAAO;YACT;YACA,OAAO,eAAe;QACxB;IACF;AAEO,MAAM,cAAc,CAAC,MAAc,MAAc,aAAqB,cAAsB,EAAE;IACnG,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,2IAAW;IAEvC,mEAAmE;IACnE,MAAM,YAAY,cACd,IAAI,sIAAS,CAAe,CAAC,kBAAkB,EAAE,aAAa,IAC9D,IAAI,sIAAS,CAAe,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAE7D,MAAM,cAAc,IAAI,gBAAgB;IAExC,sCAAsC;IACtC,OAAO,OAAO,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACnD,IAAI,QAAQ,iBAAiB,MAAM,OAAO,CAAC,QAAQ;YACjD,YAAY,GAAG,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC,QAAQ;YAC9C,YAAY,GAAG,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC,QAAQ;QAChD,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;YAC/B,MAAM,OAAO,CAAC,CAAC,MAAQ,YAAY,MAAM,CAAC,KAAK,IAAI,QAAQ;QAC7D,OAAO;YACL,YAAY,GAAG,CAAC,KAAK,MAAM,QAAQ;QACrC;IACF;IAEA,+CAA+C;IAC/C,IAAI,aAAa;QACf,YAAY,GAAG,CAAC,UAAU;IAC5B;IAEA,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,mJAAkB;YAAE;YAAM;YAAiB;YAAM;YAAa;SAAY;QACrF,SAAS,IAAM,UAAU,MAAM,CAAC;gBAAE,QAAQ,OAAO,WAAW,CAAC;YAAa;IAC5E;AACF;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,YAAY,IAAI,sIAAS,CAAqB;IAEpD,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC;YAAiB;SAAc;QAC1C,SAAS,IAAM,UAAU,GAAG,CAAC;gBAC3B,QAAQ;oBAAE,iBAAiB;gBAAc;YAC3C;QACA,SAAS,CAAC,CAAC,iBAAiB,gBAAgB;QAC5C,WAAW,OAAO,KAAK,KAAK;QAC5B,QAAQ,OAAO,KAAK,KAAK;QACzB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/header/navbar/Navbar.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"navbar__links\": \"Navbar-module-scss-module__deXsXG__navbar__links\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/navbar/Navbar.tsx"], "sourcesContent": ["import { useState } from \"react\"\r\nimport { FaAnglesDown } from \"react-icons/fa6\"\r\nimport NavigationCard from \"./navigation-card/NavigationCard\"\r\nimport { useCategories } from \"@/src/hooks/product-hooks\"\r\nimport styles from \"./Navbar.module.scss\"\r\n\r\n\r\nconst Navbar = () => {\r\n  const { isPending, error, data: categories = [] } = useCategories()\r\n  const [isOpen, setIsOpen] = useState(false)\r\n\r\n  return (\r\n    <>\r\n      <section className={styles.navbar}>\r\n        <div\r\n          className={styles.navbar__links}\r\n          onMouseOver={() => setIsOpen(true)}\r\n          onMouseLeave={() => setIsOpen(false)}\r\n        >\r\n          <button >All Products</button>\r\n          <i><FaAnglesDown /></i>\r\n        </div>\r\n      </section>\r\n      {isOpen && (\r\n        <NavigationCard\r\n          isPending={isPending}\r\n          error={error}\r\n          categories={categories}\r\n          isOpen={isOpen}\r\n          setIsOpen={setIsOpen}\r\n        />\r\n      )}\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Navbar"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAGA,MAAM,SAAS;IACb,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,aAAa,EAAE,EAAE,GAAG,IAAA,iJAAa;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IAErC,qBACE;;0BACE,8OAAC;gBAAQ,WAAW,uKAAM,CAAC,MAAM;0BAC/B,cAAA,8OAAC;oBACC,WAAW,uKAAM,CAAC,aAAa;oBAC/B,aAAa,IAAM,UAAU;oBAC7B,cAAc,IAAM,UAAU;;sCAE9B,8OAAC;sCAAQ;;;;;;sCACT,8OAAC;sCAAE,cAAA,8OAAC,+JAAY;;;;;;;;;;;;;;;;;;;;;YAGnB,wBACC,8OAAC,yLAAc;gBACb,WAAW;gBACX,OAAO;gBACP,YAAY;gBACZ,QAAQ;gBACR,WAAW;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/header/search-bar/Search.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"backdrop\": \"Search-module-scss-module__pWWbXW__backdrop\",\n  \"category_header\": \"Search-module-scss-module__pWWbXW__category_header\",\n  \"category_item\": \"Search-module-scss-module__pWWbXW__category_item\",\n  \"category_link\": \"Search-module-scss-module__pWWbXW__category_link\",\n  \"category_title\": \"Search-module-scss-module__pWWbXW__category_title\",\n  \"child_categories\": \"Search-module-scss-module__pWWbXW__child_categories\",\n  \"expand_toggle\": \"Search-module-scss-module__pWWbXW__expand_toggle\",\n  \"expandable\": \"Search-module-scss-module__pWWbXW__expandable\",\n  \"expanded\": \"Search-module-scss-module__pWWbXW__expanded\",\n  \"focused\": \"Search-module-scss-module__pWWbXW__focused\",\n  \"leaf_category\": \"Search-module-scss-module__pWWbXW__leaf_category\",\n  \"level_0\": \"Search-module-scss-module__pWWbXW__level_0\",\n  \"level_1\": \"Search-module-scss-module__pWWbXW__level_1\",\n  \"level_2\": \"Search-module-scss-module__pWWbXW__level_2\",\n  \"level_3\": \"Search-module-scss-module__pWWbXW__level_3\",\n  \"no_suggestions\": \"Search-module-scss-module__pWWbXW__no_suggestions\",\n  \"parent_category\": \"Search-module-scss-module__pWWbXW__parent_category\",\n  \"product_count\": \"Search-module-scss-module__pWWbXW__product_count\",\n  \"search\": \"Search-module-scss-module__pWWbXW__search\",\n  \"search_suggestions\": \"Search-module-scss-module__pWWbXW__search_suggestions\",\n  \"slideIn\": \"Search-module-scss-module__pWWbXW__slideIn\",\n  \"suggestions\": \"Search-module-scss-module__pWWbXW__suggestions\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/search-bar/Search.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport Link from 'next/link'\r\n// navigation via window.location used instead of next/router here\r\nimport React, { useCallback, useEffect, useRef, useState, useMemo } from 'react'\r\nimport { IoMdSearch } from 'react-icons/io'\r\n\r\nimport styles from './Search.module.scss'\r\nimport { useCategories } from '@/src/hooks/product-hooks'\r\n\r\n// Interface for the Category object\r\ninterface Category {\r\n  id: number\r\n  title: string\r\n  slug: string\r\n  level: number\r\n  parent?: number | null // The parent category, if applicable (can be null or undefined)\r\n  children?: Category[] // Nested children categories (optional)\r\n}\r\n\r\n// Enhanced Category interface with simplified properties\r\ninterface EnhancedCategory extends Category {\r\n  hasChildren: boolean\r\n  matchScore?: number // For search relevance\r\n  depth: number // Calculated hierarchy depth\r\n  children?: EnhancedCategory[] // Override to use enhanced type\r\n}\r\n\r\n// Utility function to calculate category depth in hierarchy\r\nconst calculateCategoryDepth = (\r\n  categoryId: number,\r\n  allCategories: Category[],\r\n  currentDepth = 0\r\n): number => {\r\n  const category = allCategories.find((cat) => cat.id === categoryId)\r\n  if (!category || !category.parent) {\r\n    return currentDepth\r\n  }\r\n  return calculateCategoryDepth(\r\n    category.parent,\r\n    allCategories,\r\n    currentDepth + 1\r\n  )\r\n}\r\n\r\n// Utility function to enhance categories with new properties\r\nconst enhanceCategory = (\r\n  category: Category,\r\n  allCategories: Category[]\r\n): EnhancedCategory => {\r\n  const children = allCategories.filter((cat) => cat.parent === category.id)\r\n  const hasChildren = children.length > 0\r\n  const depth = calculateCategoryDepth(category.id, allCategories)\r\n\r\n  return {\r\n    ...category,\r\n    hasChildren,\r\n    depth,\r\n    children: hasChildren\r\n      ? children.map((child) => enhanceCategory(child, allCategories))\r\n      : undefined,\r\n  }\r\n}\r\n\r\nconst Search = () => {\r\n  // Simplified state management\r\n  const [searchValue, setSearchValue] = useState<string>('')\r\n  const [showSuggestions, setShowSuggestions] = useState<boolean>(false)\r\n\r\n  // suggestions are derived later via useMemo after categories and helper definitions\r\n\r\n  // navigation via window.location (no router instance needed)\r\n\r\n  // Ref to the search container to detect clicks outside of it\r\n  const searchRef = useRef<HTMLDivElement>(null)\r\n\r\n  // Fetch all categories using the custom hook `useCategories`\r\n  const { data: categories = [] } = useCategories() as { data: Category[] }\r\n\r\n  // Enhanced recursive function to find all children for a given category\r\n  const findAllChildrenRecursive = useCallback(\r\n    (categoryId: number, allCategories: Category[]): EnhancedCategory[] => {\r\n      // Find all categories whose parent matches the given category ID\r\n      const children = allCategories.filter((cat) => cat.parent === categoryId)\r\n\r\n      // For each child, recursively find its own children and enhance them\r\n      return children.map((child) => {\r\n        const enhancedChild = enhanceCategory(child, allCategories)\r\n        return {\r\n          ...enhancedChild,\r\n          children: findAllChildrenRecursive(child.id, allCategories),\r\n        }\r\n      })\r\n    },\r\n    []\r\n  )\r\n\r\n  // Derive suggestions from searchValue + categories without storing in state to avoid update loops\r\n  const suggestedCategories = useMemo<EnhancedCategory[]>(() => {\r\n    if (!searchValue.trim()) return []\r\n\r\n    const lowerCaseSearchValue = searchValue.toLowerCase()\r\n\r\n    const matchedCategories = categories.filter((category) =>\r\n      category.title.toLowerCase().includes(lowerCaseSearchValue)\r\n    )\r\n\r\n    const enhancedCategoriesWithChildren = matchedCategories.map((category) => {\r\n      const enhanced = enhanceCategory(category, categories)\r\n      return {\r\n        ...enhanced,\r\n        children: findAllChildrenRecursive(category.id, categories),\r\n        matchScore:\r\n          lowerCaseSearchValue === category.title.toLowerCase()\r\n            ? 100\r\n            : category.title.toLowerCase().startsWith(lowerCaseSearchValue)\r\n            ? 80\r\n            : 50,\r\n      }\r\n    })\r\n\r\n    return enhancedCategoriesWithChildren.sort(\r\n      (a, b) => (b.matchScore || 0) - (a.matchScore || 0)\r\n    )\r\n  }, [searchValue, categories, findAllChildrenRecursive])\r\n\r\n  // useEffect hook to update the suggestions list based on the search input\r\n  // suggestions are derived via useMemo above; no setState inside effect required\r\n\r\n  // useEffect hook to handle clicks outside the search component\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      // If the click is outside the search box, close the suggestions dropdown\r\n      if (\r\n        searchRef.current &&\r\n        !searchRef.current.contains(event.target as Node)\r\n      ) {\r\n        setShowSuggestions(false)\r\n      }\r\n    }\r\n\r\n    // Add event listener for mouse clicks\r\n    document.addEventListener('mousedown', handleClickOutside)\r\n\r\n    // Cleanup the event listener when the component is unmounted\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside)\r\n    }\r\n  }, [])\r\n\r\n  // Handler for form submission (search submit)\r\n  const handleSearchSubmit = (event: React.FormEvent<HTMLFormElement>) => {\r\n    event.preventDefault()\r\n\r\n    // If the search value is valid (non-empty), navigate to the search results page\r\n    if (searchValue.trim()) {\r\n      // Use native navigation to avoid next/navigation typing issues in this simple case\r\n      const url = `/products/?search=${encodeURIComponent(searchValue)}`\r\n      window.location.assign(url)\r\n\r\n      // Close the suggestions dropdown\r\n      setShowSuggestions(false)\r\n    }\r\n  }\r\n\r\n  // Enhanced render function for categories with proper structure\r\n  const renderCategories = (category: EnhancedCategory) => {\r\n    return (\r\n      <div\r\n        key={category.id}\r\n        className={`${styles.category_item} ${\r\n          category.hasChildren ? styles.expandable : ''\r\n        }`}\r\n      >\r\n        <div className={styles.category_header}>\r\n          {/* Category link */}\r\n          <Link\r\n            href={`/products/category/${category.slug}`}\r\n            className={`${styles.category_link} ${\r\n              category.hasChildren\r\n                ? styles.parent_category\r\n                : styles.leaf_category\r\n            }`}\r\n            onClick={() => setShowSuggestions(false)}\r\n          >\r\n            <span\r\n              className={`${styles.category_title} ${\r\n                styles[`level_${Math.min(category.depth, 3)}`]\r\n              }`}\r\n            >\r\n              {category.title}\r\n            </span>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Always render children if they exist */}\r\n        {category.children && category.children.length > 0 && (\r\n          <div className={styles.child_categories}>\r\n            {category.children.map((child) => renderCategories(child))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className={styles.search} ref={searchRef}>\r\n      {/* Form for the search input */}\r\n      <form onSubmit={handleSearchSubmit}>\r\n        <input\r\n          type='text'\r\n          placeholder='Search...'\r\n          value={searchValue}\r\n          onChange={(e) => setSearchValue(e.target.value)}\r\n          onFocus={() => setShowSuggestions(true)}\r\n          autoComplete='off'\r\n        />\r\n        <button type='submit'>\r\n          <IoMdSearch /> {/* Search icon */}\r\n        </button>\r\n      </form>\r\n\r\n      {/* If there are suggestions to show */}\r\n      {showSuggestions && (\r\n        <div className={styles.search_suggestions}>\r\n          <div\r\n            className={styles.backdrop}\r\n            onClick={() => setShowSuggestions(false)}\r\n          ></div>{' '}\r\n          {/* Clickable backdrop to close dropdown */}\r\n          <div className={styles.suggestions}>\r\n            {/* Render the suggested categories or show a \"No suggestions\" message */}\r\n            {suggestedCategories.length > 0 ? (\r\n              suggestedCategories.map((category) => renderCategories(category))\r\n            ) : (\r\n              <p className={styles.no_suggestions}>No suggestions found</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Search\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA,kEAAkE;AAClE;AACA;AAEA;AACA;AARA;;;;;;;AA4BA,4DAA4D;AAC5D,MAAM,yBAAyB,CAC7B,YACA,eACA,eAAe,CAAC;IAEhB,MAAM,WAAW,cAAc,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;IACxD,IAAI,CAAC,YAAY,CAAC,SAAS,MAAM,EAAE;QACjC,OAAO;IACT;IACA,OAAO,uBACL,SAAS,MAAM,EACf,eACA,eAAe;AAEnB;AAEA,6DAA6D;AAC7D,MAAM,kBAAkB,CACtB,UACA;IAEA,MAAM,WAAW,cAAc,MAAM,CAAC,CAAC,MAAQ,IAAI,MAAM,KAAK,SAAS,EAAE;IACzE,MAAM,cAAc,SAAS,MAAM,GAAG;IACtC,MAAM,QAAQ,uBAAuB,SAAS,EAAE,EAAE;IAElD,OAAO;QACL,GAAG,QAAQ;QACX;QACA;QACA,UAAU,cACN,SAAS,GAAG,CAAC,CAAC,QAAU,gBAAgB,OAAO,kBAC/C;IACN;AACF;AAEA,MAAM,SAAS;IACb,8BAA8B;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAS;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAU;IAEhE,oFAAoF;IAEpF,6DAA6D;IAE7D,6DAA6D;IAC7D,MAAM,YAAY,IAAA,+MAAM,EAAiB;IAEzC,6DAA6D;IAC7D,MAAM,EAAE,MAAM,aAAa,EAAE,EAAE,GAAG,IAAA,iJAAa;IAE/C,wEAAwE;IACxE,MAAM,2BAA2B,IAAA,oNAAW,EAC1C,CAAC,YAAoB;QACnB,iEAAiE;QACjE,MAAM,WAAW,cAAc,MAAM,CAAC,CAAC,MAAQ,IAAI,MAAM,KAAK;QAE9D,qEAAqE;QACrE,OAAO,SAAS,GAAG,CAAC,CAAC;YACnB,MAAM,gBAAgB,gBAAgB,OAAO;YAC7C,OAAO;gBACL,GAAG,aAAa;gBAChB,UAAU,yBAAyB,MAAM,EAAE,EAAE;YAC/C;QACF;IACF,GACA,EAAE;IAGJ,kGAAkG;IAClG,MAAM,sBAAsB,IAAA,gNAAO,EAAqB;QACtD,IAAI,CAAC,YAAY,IAAI,IAAI,OAAO,EAAE;QAElC,MAAM,uBAAuB,YAAY,WAAW;QAEpD,MAAM,oBAAoB,WAAW,MAAM,CAAC,CAAC,WAC3C,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QAGxC,MAAM,iCAAiC,kBAAkB,GAAG,CAAC,CAAC;YAC5D,MAAM,WAAW,gBAAgB,UAAU;YAC3C,OAAO;gBACL,GAAG,QAAQ;gBACX,UAAU,yBAAyB,SAAS,EAAE,EAAE;gBAChD,YACE,yBAAyB,SAAS,KAAK,CAAC,WAAW,KAC/C,MACA,SAAS,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,wBACxC,KACA;YACR;QACF;QAEA,OAAO,+BAA+B,IAAI,CACxC,CAAC,GAAG,IAAM,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;IAEtD,GAAG;QAAC;QAAa;QAAY;KAAyB;IAEtD,0EAA0E;IAC1E,gFAAgF;IAEhF,+DAA+D;IAC/D,IAAA,kNAAS,EAAC;QACR,MAAM,qBAAqB,CAAC;YAC1B,yEAAyE;YACzE,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GACxC;gBACA,mBAAmB;YACrB;QACF;QAEA,sCAAsC;QACtC,SAAS,gBAAgB,CAAC,aAAa;QAEvC,6DAA6D;QAC7D,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc;QAEpB,gFAAgF;QAChF,IAAI,YAAY,IAAI,IAAI;YACtB,mFAAmF;YACnF,MAAM,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,cAAc;YAClE,OAAO,QAAQ,CAAC,MAAM,CAAC;YAEvB,iCAAiC;YACjC,mBAAmB;QACrB;IACF;IAEA,gEAAgE;IAChE,MAAM,mBAAmB,CAAC;QACxB,qBACE,8OAAC;YAEC,WAAW,GAAG,8KAAM,CAAC,aAAa,CAAC,CAAC,EAClC,SAAS,WAAW,GAAG,8KAAM,CAAC,UAAU,GAAG,IAC3C;;8BAEF,8OAAC;oBAAI,WAAW,8KAAM,CAAC,eAAe;8BAEpC,cAAA,8OAAC,uKAAI;wBACH,MAAM,CAAC,mBAAmB,EAAE,SAAS,IAAI,EAAE;wBAC3C,WAAW,GAAG,8KAAM,CAAC,aAAa,CAAC,CAAC,EAClC,SAAS,WAAW,GAChB,8KAAM,CAAC,eAAe,GACtB,8KAAM,CAAC,aAAa,EACxB;wBACF,SAAS,IAAM,mBAAmB;kCAElC,cAAA,8OAAC;4BACC,WAAW,GAAG,8KAAM,CAAC,cAAc,CAAC,CAAC,EACnC,8KAAM,CAAC,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,EAC9C;sCAED,SAAS,KAAK;;;;;;;;;;;;;;;;gBAMpB,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,mBAC/C,8OAAC;oBAAI,WAAW,8KAAM,CAAC,gBAAgB;8BACpC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAU,iBAAiB;;;;;;;WA7BlD,SAAS,EAAE;;;;;IAkCtB;IAEA,qBACE,8OAAC;QAAI,WAAW,8KAAM,CAAC,MAAM;QAAE,KAAK;;0BAElC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,SAAS,IAAM,mBAAmB;wBAClC,cAAa;;;;;;kCAEf,8OAAC;wBAAO,MAAK;;0CACX,8OAAC,4JAAU;;;;;4BAAG;;;;;;;;;;;;;YAKjB,iCACC,8OAAC;gBAAI,WAAW,8KAAM,CAAC,kBAAkB;;kCACvC,8OAAC;wBACC,WAAW,8KAAM,CAAC,QAAQ;wBAC1B,SAAS,IAAM,mBAAmB;;;;;;oBAC5B;kCAER,8OAAC;wBAAI,WAAW,8KAAM,CAAC,WAAW;kCAE/B,oBAAoB,MAAM,GAAG,IAC5B,oBAAoB,GAAG,CAAC,CAAC,WAAa,iBAAiB,2BAEvD,8OAAC;4BAAE,WAAW,8KAAM,CAAC,cAAc;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAOnD;uCAEe", "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/header/Header.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"Header-module-scss-module__yUQ6Nq__active\",\n  \"cart\": \"Header-module-scss-module__yUQ6Nq__cart\",\n  \"divider\": \"Header-module-scss-module__yUQ6Nq__divider\",\n  \"dropdown\": \"Header-module-scss-module__yUQ6Nq__dropdown\",\n  \"dropdown_container\": \"Header-module-scss-module__yUQ6Nq__dropdown_container\",\n  \"dropdown_footer\": \"Header-module-scss-module__yUQ6Nq__dropdown_footer\",\n  \"dropdown_header\": \"Header-module-scss-module__yUQ6Nq__dropdown_header\",\n  \"dropdown_menu\": \"Header-module-scss-module__yUQ6Nq__dropdown_menu\",\n  \"header\": \"Header-module-scss-module__yUQ6Nq__header\",\n  \"header__badge\": \"Header-module-scss-module__yUQ6Nq__header__badge\",\n  \"header__bottom_nav\": \"Header-module-scss-module__yUQ6Nq__header__bottom_nav\",\n  \"header__end\": \"Header-module-scss-module__yUQ6Nq__header__end\",\n  \"header__icon\": \"Header-module-scss-module__yUQ6Nq__header__icon\",\n  \"header__login\": \"Header-module-scss-module__yUQ6Nq__header__login\",\n  \"header__login_links\": \"Header-module-scss-module__yUQ6Nq__header__login_links\",\n  \"header__logo\": \"Header-module-scss-module__yUQ6Nq__header__logo\",\n  \"header__search\": \"Header-module-scss-module__yUQ6Nq__header__search\",\n  \"header__sign_in\": \"Header-module-scss-module__yUQ6Nq__header__sign_in\",\n  \"header__top\": \"Header-module-scss-module__yUQ6Nq__header__top\",\n  \"menu_item\": \"Header-module-scss-module__yUQ6Nq__menu_item\",\n  \"warning_link\": \"Header-module-scss-module__yUQ6Nq__warning_link\",\n  \"wishlist\": \"Header-module-scss-module__yUQ6Nq__wishlist\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useLogout } from \"@/src/hooks/auth-hooks\"\nimport { useCart } from \"@/src/hooks/cart-hooks\"\nimport { useCustomerDetails } from \"@/src/hooks/customer-hooks\"\nimport { useWishlist } from \"@/src/hooks/wishlist-hooks\"\nimport authStore from \"@/src/stores/auth-store\"\nimport Link from \"next/link\"\nimport { useEffect, useState } from \"react\"\nimport { BsBox2 } from \"react-icons/bs\"\nimport { FaCaretDown, FaCartPlus, FaHeart } from \"react-icons/fa\"\nimport { MdOutlineAccountCircle } from \"react-icons/md\"\nimport { RiLogoutCircleRLine } from \"react-icons/ri\"\nimport { CartItemShape } from \"../../types/store-types\"\nimport Logo from \"../utils/logo/Logo\"\nimport Navbar from \"./navbar/Navbar\"\nimport Search from \"./search-bar/Search\"\nimport styles from \"./Header.module.scss\"\n\n\nconst Header = () => {\n  const [isOpen, setIsOpen] = useState(false)\n  const { data } = useCart()\n  const { isLoggedIn } = authStore()\n\n  const { mutation } = useLogout()\n  const { data: wishlistItems } = useWishlist(1, isLoggedIn)\n  const { data: customerData } = useCustomerDetails(isLoggedIn)\n\n  const handleLinkClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\n    if ((event.target as HTMLElement).tagName === \"A\") {\n      setIsOpen(false)\n    }\n  }\n\n  const handleLogout = () => {\n    mutation.mutate()\n  }\n\n  const cartItemsQuantity = (cart_items: CartItemShape[]) => {\n    let item_qty = 0\n    if (cart_items) {\n      cart_items.forEach((item) => {\n        item_qty += item.quantity\n      })\n    }\n    return item_qty\n  }\n  useEffect(() => {\n\n  }, [customerData, isLoggedIn])\n  return (\n    <>\n      <div className={styles.header}>\n        <section className={`${styles.header__top} container`}>\n          {/* Logo section */}\n          <section className={styles.header__logo}>\n            <Logo />\n          </section>\n\n          {/* Search section */}\n          <section className={styles.header__search}>\n            <Search />\n          </section>\n\n          {/* Wishlist, Cart, Signup, Dropdown section */}\n          <section className={styles.header__end}>\n            <Link href='/account/wishlist' title='wishlist' className={styles.wishlist}>\n              <p className={styles.header__badge}>\n                <span>{wishlistItems?.count || 0}</span>\n              </p>\n              <i className={styles.header__icon}><FaHeart /></i>\n              {/* <Link href='/account/wishlist/'>Wishlist {`|`}</Link> */}\n            </Link>\n\n            <Link href='/checkout/cart' className={styles.cart} title='cart'>\n              <p className={styles.header__badge}><span>{cartItemsQuantity(data?.cart_items || [])}</span></p>\n              <i className={styles.header__icon}><FaCartPlus /></i>\n              {/* <Link href='/cart/'>Cart {`|`}</Link> */}\n            </Link>\n\n            <div\n              className={styles.header__sign_in}\n              onMouseEnter={() => isLoggedIn && setIsOpen(true)}\n              onMouseLeave={() => isLoggedIn && setIsOpen(false)}\n            >\n              <div\n                className={`${styles.header__login} ${!isLoggedIn ? styles.header__login_links : ''}`}\n              >\n                <p>\n                  {!isLoggedIn && (\n                    <>\n                      <Link href='/login/'>Sign In</Link>\n                      {\" | \"}\n                      <Link href='/register/initiate/'>Sign Up</Link>\n                    </>\n                  )}\n                  {isLoggedIn && customerData?.first_name && (\n                    <>Hello, {customerData.first_name}</>\n                  )}\n                  {isLoggedIn && !customerData?.first_name && (\n                    <Link className={styles.warning_link} href='/account/profile'>!Complete your profile</Link>\n                  )}\n                </p>\n                {isLoggedIn && <i><FaCaretDown /></i>}\n              </div>\n              {isOpen && isLoggedIn &&\n                <div\n                  className={`${styles.dropdown_container} ${isOpen ? styles.active : ''}`}\n                  onClick={handleLinkClick}\n                >\n                  <div className={styles.dropdown}>\n                    <div className={styles.dropdown_header}>\n                      <h4>Welcome back, {customerData?.first_name || 'User'}</h4>\n                    </div>\n                    <div className={styles.dropdown_menu}>\n                      <div className={styles.menu_item}>\n                        <i><MdOutlineAccountCircle /></i>\n                        <Link href='/account/profile'>My Account</Link>\n                      </div>\n                      <div className={styles.menu_item}>\n                        <i><BsBox2 /></i>\n                        <Link href='/account/orders'>My Orders</Link>\n                      </div>\n                      <div className={styles.menu_item}>\n                        <i><BsBox2 /></i>\n                        <Link href='/account/wishlist'>My WishList</Link>\n                      </div>\n                      {/* <div className={styles.divider}></div>\n                      <div className={styles.menu_item}>\n                        <i><BsBox2 /></i>\n                        <Link href='/'>My Reviews</Link>\n                      </div> */}\n                    </div>\n                    <div className={styles.dropdown_footer}>\n                      <div className={styles.menu_item} onClick={handleLogout}>\n                        <i><RiLogoutCircleRLine /></i>\n                        <span>Sign Out</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              }\n            </div>\n          </section>\n        </section>\n      </div>\n      <div className={styles.header__bottom_nav}>\n        <section className={`${styles.header__bottom} container`}>\n          <Navbar />\n        </section>\n      </div>\n    </>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;AAoBA,MAAM,SAAS;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IACrC,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,wIAAO;IACxB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,yIAAS;IAEhC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAA,0IAAS;IAC9B,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,IAAA,gJAAW,EAAC,GAAG;IAC/C,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,IAAA,uJAAkB,EAAC;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,AAAC,MAAM,MAAM,CAAiB,OAAO,KAAK,KAAK;YACjD,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB,SAAS,MAAM;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,WAAW;QACf,IAAI,YAAY;YACd,WAAW,OAAO,CAAC,CAAC;gBAClB,YAAY,KAAK,QAAQ;YAC3B;QACF;QACA,OAAO;IACT;IACA,IAAA,kNAAS,EAAC,KAEV,GAAG;QAAC;QAAc;KAAW;IAC7B,qBACE;;0BACE,8OAAC;gBAAI,WAAW,6JAAM,CAAC,MAAM;0BAC3B,cAAA,8OAAC;oBAAQ,WAAW,GAAG,6JAAM,CAAC,WAAW,CAAC,UAAU,CAAC;;sCAEnD,8OAAC;4BAAQ,WAAW,6JAAM,CAAC,YAAY;sCACrC,cAAA,8OAAC,sJAAI;;;;;;;;;;sCAIP,8OAAC;4BAAQ,WAAW,6JAAM,CAAC,cAAc;sCACvC,cAAA,8OAAC,kKAAM;;;;;;;;;;sCAIT,8OAAC;4BAAQ,WAAW,6JAAM,CAAC,WAAW;;8CACpC,8OAAC,uKAAI;oCAAC,MAAK;oCAAoB,OAAM;oCAAW,WAAW,6JAAM,CAAC,QAAQ;;sDACxE,8OAAC;4CAAE,WAAW,6JAAM,CAAC,aAAa;sDAChC,cAAA,8OAAC;0DAAM,eAAe,SAAS;;;;;;;;;;;sDAEjC,8OAAC;4CAAE,WAAW,6JAAM,CAAC,YAAY;sDAAE,cAAA,8OAAC,yJAAO;;;;;;;;;;;;;;;;8CAI7C,8OAAC,uKAAI;oCAAC,MAAK;oCAAiB,WAAW,6JAAM,CAAC,IAAI;oCAAE,OAAM;;sDACxD,8OAAC;4CAAE,WAAW,6JAAM,CAAC,aAAa;sDAAE,cAAA,8OAAC;0DAAM,kBAAkB,MAAM,cAAc,EAAE;;;;;;;;;;;sDACnF,8OAAC;4CAAE,WAAW,6JAAM,CAAC,YAAY;sDAAE,cAAA,8OAAC,4JAAU;;;;;;;;;;;;;;;;8CAIhD,8OAAC;oCACC,WAAW,6JAAM,CAAC,eAAe;oCACjC,cAAc,IAAM,cAAc,UAAU;oCAC5C,cAAc,IAAM,cAAc,UAAU;;sDAE5C,8OAAC;4CACC,WAAW,GAAG,6JAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,aAAa,6JAAM,CAAC,mBAAmB,GAAG,IAAI;;8DAErF,8OAAC;;wDACE,CAAC,4BACA;;8EACE,8OAAC,uKAAI;oEAAC,MAAK;8EAAU;;;;;;gEACpB;8EACD,8OAAC,uKAAI;oEAAC,MAAK;8EAAsB;;;;;;;;wDAGpC,cAAc,cAAc,4BAC3B;;gEAAE;gEAAQ,aAAa,UAAU;;;wDAElC,cAAc,CAAC,cAAc,4BAC5B,8OAAC,uKAAI;4DAAC,WAAW,6JAAM,CAAC,YAAY;4DAAE,MAAK;sEAAmB;;;;;;;;;;;;gDAGjE,4BAAc,8OAAC;8DAAE,cAAA,8OAAC,6JAAW;;;;;;;;;;;;;;;;wCAE/B,UAAU,4BACT,8OAAC;4CACC,WAAW,GAAG,6JAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE,SAAS,6JAAM,CAAC,MAAM,GAAG,IAAI;4CACxE,SAAS;sDAET,cAAA,8OAAC;gDAAI,WAAW,6JAAM,CAAC,QAAQ;;kEAC7B,8OAAC;wDAAI,WAAW,6JAAM,CAAC,eAAe;kEACpC,cAAA,8OAAC;;gEAAG;gEAAe,cAAc,cAAc;;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAW,6JAAM,CAAC,aAAa;;0EAClC,8OAAC;gEAAI,WAAW,6JAAM,CAAC,SAAS;;kFAC9B,8OAAC;kFAAE,cAAA,8OAAC,wKAAsB;;;;;;;;;;kFAC1B,8OAAC,uKAAI;wEAAC,MAAK;kFAAmB;;;;;;;;;;;;0EAEhC,8OAAC;gEAAI,WAAW,6JAAM,CAAC,SAAS;;kFAC9B,8OAAC;kFAAE,cAAA,8OAAC,wJAAM;;;;;;;;;;kFACV,8OAAC,uKAAI;wEAAC,MAAK;kFAAkB;;;;;;;;;;;;0EAE/B,8OAAC;gEAAI,WAAW,6JAAM,CAAC,SAAS;;kFAC9B,8OAAC;kFAAE,cAAA,8OAAC,wJAAM;;;;;;;;;;kFACV,8OAAC,uKAAI;wEAAC,MAAK;kFAAoB;;;;;;;;;;;;;;;;;;kEAQnC,8OAAC;wDAAI,WAAW,6JAAM,CAAC,eAAe;kEACpC,cAAA,8OAAC;4DAAI,WAAW,6JAAM,CAAC,SAAS;4DAAE,SAAS;;8EACzC,8OAAC;8EAAE,cAAA,8OAAC,qKAAmB;;;;;;;;;;8EACvB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxB,8OAAC;gBAAI,WAAW,6JAAM,CAAC,kBAAkB;0BACvC,cAAA,8OAAC;oBAAQ,WAAW,GAAG,6JAAM,CAAC,cAAc,CAAC,UAAU,CAAC;8BACtD,cAAA,8OAAC,2JAAM;;;;;;;;;;;;;;;;;AAKjB;uCAEe", "debugId": null}}]}