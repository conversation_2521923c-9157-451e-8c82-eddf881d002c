{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"address_selection\": \"AddressStage-module-scss-module__cybDRG__address_selection\",\n  \"address_stage\": \"AddressStage-module-scss-module__cybDRG__address_stage\",\n  \"cart\": \"AddressStage-module-scss-module__cybDRG__cart\",\n  \"contact_details\": \"AddressStage-module-scss-module__cybDRG__contact_details\",\n  \"missing_addresses\": \"AddressStage-module-scss-module__cybDRG__missing_addresses\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"action_button\": \"EmptyCart-module-scss-module__p5kxma__action_button\",\n  \"action_section\": \"EmptyCart-module-scss-module__p5kxma__action_section\",\n  \"content_section\": \"EmptyCart-module-scss-module__p5kxma__content_section\",\n  \"description\": \"EmptyCart-module-scss-module__p5kxma__description\",\n  \"empty_cart\": \"EmptyCart-module-scss-module__p5kxma__empty_cart\",\n  \"heading\": \"EmptyCart-module-scss-module__p5kxma__heading\",\n  \"icon_section\": \"EmptyCart-module-scss-module__p5kxma__icon_section\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/empty-cart/EmptyCart.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport { FaShoppingCart } from 'react-icons/fa'\r\nimport styles from './EmptyCart.module.scss'\r\n\r\ninterface Props {\r\n  message?: string\r\n  showIcon?: boolean\r\n  actionText?: string\r\n  actionHref?: string\r\n}\r\n\r\nconst EmptyCart = ({\r\n  message,\r\n  showIcon = true,\r\n  actionText = \"Go Shopping\",\r\n  actionHref = \"/\"\r\n}: Props) => {\r\n  return (\r\n    <div className={styles.empty_cart} role=\"region\" aria-label=\"Empty cart\">\r\n      {showIcon && (\r\n        <div className={styles.icon_section} aria-hidden=\"true\">\r\n          <FaShoppingCart />\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.content_section}>\r\n        <h2 className={styles.heading}>\r\n          {message ? message : \"Your cart is empty\"}\r\n        </h2>\r\n        <p className={styles.description}>\r\n          Add some products to your cart to get started with your shopping journey.\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.action_section}>\r\n        <Link\r\n          href={actionHref}\r\n          className={styles.action_button}\r\n          aria-label={`${actionText} - Browse products to add to your cart`}\r\n        >\r\n          {actionText}\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\nexport default EmptyCart"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AASA,MAAM,YAAY,CAAC,EACjB,OAAO,EACP,WAAW,IAAI,EACf,aAAa,aAAa,EAC1B,aAAa,GAAG,EACV;IACN,qBACE,8OAAC;QAAI,WAAW,gLAAM,CAAC,UAAU;QAAE,MAAK;QAAS,cAAW;;YACzD,0BACC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,YAAY;gBAAE,eAAY;0BAC/C,cAAA,8OAAC,gKAAc;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAW,gLAAM,CAAC,eAAe;;kCACpC,8OAAC;wBAAG,WAAW,gLAAM,CAAC,OAAO;kCAC1B,UAAU,UAAU;;;;;;kCAEvB,8OAAC;wBAAE,WAAW,gLAAM,CAAC,WAAW;kCAAE;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,cAAc;0BACnC,cAAA,8OAAC,uKAAI;oBACH,MAAM;oBACN,WAAW,gLAAM,CAAC,aAAa;oBAC/B,cAAY,GAAG,WAAW,sCAAsC,CAAC;8BAEhE;;;;;;;;;;;;;;;;;AAKX;uCACe", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/underlay/Underlay.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"underlay\": \"Underlay-module-scss-module__PkWa8a__underlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.tsx"], "sourcesContent": ["import React from 'react'\r\nimport styles from './Underlay.module.scss'\r\n\r\ninterface Props {\r\n  children: React.ReactNode\r\n  isOpen: boolean\r\n  onClose?: () => void\r\n  bgOpacity?: number\r\n  // zIndex?: number\r\n}\r\n\r\nconst Underlay = ({ children, isOpen, onClose, bgOpacity = 0.3 }: Props) => {\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    if (e.target === e.currentTarget && onClose) {\r\n      onClose()\r\n    }\r\n  }\r\n\r\n  return isOpen ? (\r\n    <div\r\n      className={styles.underlay}\r\n      style={{ backgroundColor: `rgba(0, 0, 0, ${bgOpacity})` }}\r\n      onClick={handleOverlayClick}\r\n    >\r\n      {children}\r\n    </div>\r\n  ) : null\r\n}\r\n\r\nexport default Underlay"], "names": [], "mappings": ";;;;;AACA;;;AAUA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,GAAG,EAAS;IAErE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,SAAS;YAC3C;QACF;IACF;IAEA,OAAO,uBACL,8OAAC;QACC,WAAW,0KAAM,CAAC,QAAQ;QAC1B,OAAO;YAAE,iBAAiB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAAC;QACxD,SAAS;kBAER;;;;;mDAED;AACN;uCAEe", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/spinner/Spinner.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport SyncLoader from 'react-spinners/SyncLoader'\r\nimport <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader'\r\nimport PulseLoader from 'react-spinners/PulseLoader'\r\nimport RiseLoader from 'react-spinners/RiseLoader'\r\nimport RotateLoader from 'react-spinners/RotateLoader'\r\nimport ScaleLoader from 'react-spinners/ScaleLoader'\r\nimport CircleLoader from 'react-spinners/CircleLoader'\r\nimport Underlay from '../underlay/Underlay'\r\n\r\ninterface Props {\r\n  loading: boolean\r\n  color?: string\r\n  size?: number\r\n  thickness?: number\r\n  bgOpacity?: number\r\n  useUnderlay?: boolean\r\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\r\n}\r\n\r\nconst Spinner = ({\r\n  loading,\r\n  color,\r\n  size = 150,\r\n  thickness = 5,\r\n  bgOpacity,\r\n  useUnderlay = false,\r\n  spinnerType = 'sync'\r\n}: Props) => {\r\n  const renderSpinner = () => {\r\n    const commonProps = {\r\n      color,\r\n      loading,\r\n      size,\r\n      thickness,\r\n      'aria-label': 'Loading Spinner',\r\n    }\r\n\r\n    switch (spinnerType) {\r\n      case 'clip':\r\n        return <ClipLoader {...commonProps} />\r\n      case 'pulse':\r\n        return <PulseLoader {...commonProps} />\r\n      case 'rise':\r\n        return <RiseLoader {...commonProps} />\r\n      case 'rotate':\r\n        return <RotateLoader {...commonProps} />\r\n      case 'scale':\r\n        return <ScaleLoader {...commonProps} />\r\n      case 'circle':\r\n        return <CircleLoader {...commonProps} />\r\n      case 'sync':\r\n      default:\r\n        return <SyncLoader {...commonProps} />\r\n    }\r\n  }\r\n\r\n  if (!useUnderlay) {\r\n    return loading ? renderSpinner() : null\r\n  }\r\n\r\n  return (\r\n    <Underlay isOpen={loading} bgOpacity={bgOpacity}>\r\n      {renderSpinner()}\r\n    </Underlay>\r\n  )\r\n}\r\n\r\nexport default Spinner"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,UAAU,CAAC,EACf,OAAO,EACP,KAAK,EACL,OAAO,GAAG,EACV,YAAY,CAAC,EACb,SAAS,EACT,cAAc,KAAK,EACnB,cAAc,MAAM,EACd;IACN,MAAM,gBAAgB;QACpB,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA,cAAc;QAChB;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;YACL;gBACE,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;QACtC;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,8OAAC,8JAAQ;QAAC,QAAQ;QAAS,WAAW;kBACnC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/TextLimit.tsx"], "sourcesContent": ["interface Props {\r\n  title: string\r\n  maxLength: number\r\n}\r\n\r\nconst LimitTitleLength = ({ title, maxLength }: Props) => {\r\n  function limitTitleLength(title: string, maxLength: number) {\r\n    if (title.length > maxLength) {\r\n      return title.substring(0, maxLength) + '...'\r\n    }\r\n    return title\r\n  }\r\n\r\n  return <>{limitTitleLength(title, maxLength)}</>\r\n}\r\n\r\nexport default LimitTitleLength"], "names": [], "mappings": ";;;;;;AAKA,MAAM,mBAAmB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAS;IACnD,SAAS,iBAAiB,KAAa,EAAE,SAAiB;QACxD,IAAI,MAAM,MAAM,GAAG,WAAW;YAC5B,OAAO,MAAM,SAAS,CAAC,GAAG,aAAa;QACzC;QACA,OAAO;IACT;IAEA,qBAAO;kBAAG,iBAAiB,OAAO;;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/cart-selection-hooks.ts"], "sourcesContent": ["import { useMutation, useQueryClient } from '@tanstack/react-query'\nimport { useCallback } from 'react'\nimport { AxiosError } from 'axios'\nimport APIClient from '../lib/api-client'\nimport cartStore from '../stores/cart-store'\nimport { CACHE_KEY_CART_ITEMS } from '../constants/constants'\nimport { ErrorResponse } from '../types/types'\n\ninterface CartItemSelectionRequest {\n  is_selected: boolean\n}\n\ninterface BulkSelectionRequest {\n  item_ids?: number[]\n  select_all?: boolean\n  deselect_all?: boolean\n}\n\ninterface SelectionResponse {\n  success: boolean\n  updated_count?: number\n  message: string\n  id?: number\n  is_selected?: boolean\n}\n\nexport const useCartItemSelection = () => {\n  const { cartId } = cartStore()\n  const queryClient = useQueryClient()\n\n  return useMutation<\n    SelectionResponse,\n    AxiosError<ErrorResponse>,\n    { itemId: number; isSelected: boolean }\n  >({\n    mutationFn: async ({ itemId, isSelected }) => {\n      const apiClient = new APIClient<\n        SelectionResponse,\n        CartItemSelectionRequest\n      >(`/cart/${cartId}/items/${itemId}/select/`)\n      return apiClient.patch({ is_selected: isSelected })\n    },\n    // Optimistic updates: apply selection change locally before server responds\n    onMutate: async (variables) => {\n      const { itemId, isSelected } = variables\n      if (!cartId)\n        return {\n          previousCart: null,\n          previousSelected: cartStore.getState().selectedItemIds || [],\n        }\n\n      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)\n      await queryClient.cancelQueries({\n        queryKey: [CACHE_KEY_CART_ITEMS, cartId],\n      })\n\n      const previousCart = queryClient.getQueryData<unknown>([\n        CACHE_KEY_CART_ITEMS,\n        cartId,\n      ])\n\n      // Optimistically update the cached cart items if shape matches { cart_items: Array }\n      if (previousCart && typeof previousCart === 'object') {\n        const prevObj = previousCart as Record<string, unknown>\n        if (Array.isArray(prevObj.cart_items)) {\n          const prev = previousCart as {\n            cart_items: Array<Record<string, unknown>>\n          }\n          const newCart = {\n            ...prev,\n            cart_items: prev.cart_items.map((it) => {\n              // runtime-safe check and shallow update\n              if (it && typeof it === 'object') {\n                const rec = it as Record<string, unknown>\n                if (rec.id !== undefined && Number(rec.id) === itemId) {\n                  return { ...rec, is_selected: isSelected }\n                }\n              }\n              return it\n            }),\n          }\n          queryClient.setQueryData([CACHE_KEY_CART_ITEMS, cartId], newCart)\n        }\n      }\n\n      // Optimistically update local persisted selection array\n      const currentSelection = cartStore.getState().selectedItemIds || []\n      const has = currentSelection.includes(itemId)\n      let newSelection = currentSelection\n      if (isSelected && !has) newSelection = [...currentSelection, itemId]\n      else if (!isSelected && has)\n        newSelection = currentSelection.filter((id) => id !== itemId)\n      cartStore.getState().setSelectedItems(newSelection)\n\n      return { previousCart, previousSelected: currentSelection }\n    },\n    onError: (\n      error: AxiosError<ErrorResponse>,\n      variables: { itemId: number; isSelected: boolean },\n      context: unknown\n    ) => {\n      console.error('Error updating item selection:', error)\n      // rollback cache\n      if (cartId && context && typeof context === 'object') {\n        const ctx = context as {\n          previousCart?: unknown\n          previousSelected?: number[]\n        }\n        if (ctx.previousCart) {\n          queryClient.setQueryData(\n            [CACHE_KEY_CART_ITEMS, cartId],\n            ctx.previousCart\n          )\n        }\n        if (ctx.previousSelected) {\n          cartStore.getState().setSelectedItems(ctx.previousSelected)\n        }\n      }\n    },\n    onSettled: () => {\n      // Ensure we refetch fresh data from the server to reconcile\n      if (cartId) {\n        queryClient.invalidateQueries({\n          queryKey: [CACHE_KEY_CART_ITEMS, cartId],\n        })\n      }\n    },\n  })\n}\n\nexport const useBulkCartSelection = () => {\n  const { cartId } = cartStore()\n  const queryClient = useQueryClient()\n\n  const bulkSelectMutation = useMutation<\n    SelectionResponse,\n    AxiosError<ErrorResponse>,\n    BulkSelectionRequest\n  >({\n    mutationFn: async (data) => {\n      const apiClient = new APIClient<SelectionResponse, BulkSelectionRequest>(\n        `/cart/${cartId}/items/bulk-select/`\n      )\n      return apiClient.post(data)\n    },\n    onSuccess: (data, variables) => {\n      // Update local store based on operation\n      if (variables.select_all) {\n        // This will be handled by the parent component that knows all item IDs\n      } else if (variables.item_ids) {\n        cartStore.getState().setSelectedItems(variables.item_ids)\n      }\n\n      // Invalidate cart queries to refresh data\n      queryClient.invalidateQueries({\n        queryKey: [CACHE_KEY_CART_ITEMS, cartId],\n      })\n    },\n    onError: (error) => {\n      console.error('Error bulk selecting items:', error)\n    },\n  })\n\n  const bulkDeselectMutation = useMutation<\n    SelectionResponse,\n    AxiosError<ErrorResponse>,\n    BulkSelectionRequest\n  >({\n    mutationFn: async (data) => {\n      const apiClient = new APIClient<SelectionResponse, BulkSelectionRequest>(\n        `/cart/${cartId}/items/bulk-deselect/`\n      )\n      return apiClient.post(data)\n    },\n    onSuccess: (data, variables) => {\n      // Update local store based on operation\n      if (variables.deselect_all) {\n        cartStore.getState().clearSelection()\n      } else if (variables.item_ids) {\n        // Remove specific items from selection\n        const currentSelection = cartStore.getState().selectedItemIds || []\n        const newSelection = currentSelection.filter(\n          (id) => !variables.item_ids?.includes(id)\n        )\n        cartStore.getState().setSelectedItems(newSelection)\n      }\n\n      // Invalidate cart queries to refresh data\n      queryClient.invalidateQueries({\n        queryKey: [CACHE_KEY_CART_ITEMS, cartId],\n      })\n    },\n    onError: (error) => {\n      console.error('Error bulk deselecting items:', error)\n    },\n  })\n\n  return {\n    bulkSelect: bulkSelectMutation.mutate,\n    bulkDeselect: bulkDeselectMutation.mutate,\n    isSelectLoading: bulkSelectMutation.isPending,\n    isDeselectLoading: bulkDeselectMutation.isPending,\n    selectError: bulkSelectMutation.error,\n    deselectError: bulkDeselectMutation.error,\n  }\n}\n\nexport const useSelectedCartSummary = () => {\n  const { cartId } = cartStore()\n\n  const fetchSummary = useCallback(() => {\n    const apiClient = new APIClient<{\n      selected_items_count: number\n      total_items_count: number\n      selected_total_price: number\n      selected_total_weight: number\n      all_items_selected: boolean\n      selected_item_ids: number[]\n    }>(`/cart/${cartId}/selected_summary/`)\n\n    return apiClient.get()\n  }, [cartId])\n\n  return { fetchSummary }\n}\n\n// Hook to sync frontend selection state with backend\n// export const useSyncCartSelection = () => {\n//   // avoid subscribing to the store here so the returned function identity is stable\n//   const getSelected = () => cartStore.getState().selectedItemIds || []\n//   const setSelected = (ids: number[]) =>\n//     cartStore.getState().setSelectedItems(ids)\n\n//   const syncWithBackend = useCallback(\n//     (cartItems: Array<{ id: number; is_selected?: boolean }>) => {\n//       // Compute backend selected ids\n//       const backendSelectedIds = cartItems\n//         .filter((item) => item.is_selected)\n//         .map((item) => item.id)\n\n//       // Compare lengths first for a cheap check, then confirm all ids match (order-insensitive)\n//       const current = getSelected()\n//       if (\n//         backendSelectedIds.length === current.length &&\n//         backendSelectedIds.every((id) => current.includes(id))\n//       ) {\n//         // No change -> avoid calling setSelected to prevent triggering effects\n//         return\n//       }\n\n//       // Only update when backend selection differs from current\n//       setSelected(backendSelectedIds)\n//     },\n//     []\n//   )\n\n//   return { syncWithBackend }\n// }\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;;;;AAqBO,MAAM,uBAAuB;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,cAAc,IAAA,wMAAc;IAElC,OAAO,IAAA,6LAAW,EAIhB;QACA,YAAY,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE;YACvC,MAAM,YAAY,IAAI,sIAAS,CAG7B,CAAC,MAAM,EAAE,OAAO,OAAO,EAAE,OAAO,QAAQ,CAAC;YAC3C,OAAO,UAAU,KAAK,CAAC;gBAAE,aAAa;YAAW;QACnD;QACA,4EAA4E;QAC5E,UAAU,OAAO;YACf,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;YAC/B,IAAI,CAAC,QACH,OAAO;gBACL,cAAc;gBACd,kBAAkB,yIAAS,CAAC,QAAQ,GAAG,eAAe,IAAI,EAAE;YAC9D;YAEF,gFAAgF;YAChF,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU;oBAAC,qJAAoB;oBAAE;iBAAO;YAC1C;YAEA,MAAM,eAAe,YAAY,YAAY,CAAU;gBACrD,qJAAoB;gBACpB;aACD;YAED,qFAAqF;YACrF,IAAI,gBAAgB,OAAO,iBAAiB,UAAU;gBACpD,MAAM,UAAU;gBAChB,IAAI,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;oBACrC,MAAM,OAAO;oBAGb,MAAM,UAAU;wBACd,GAAG,IAAI;wBACP,YAAY,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC;4BAC/B,wCAAwC;4BACxC,IAAI,MAAM,OAAO,OAAO,UAAU;gCAChC,MAAM,MAAM;gCACZ,IAAI,IAAI,EAAE,KAAK,aAAa,OAAO,IAAI,EAAE,MAAM,QAAQ;oCACrD,OAAO;wCAAE,GAAG,GAAG;wCAAE,aAAa;oCAAW;gCAC3C;4BACF;4BACA,OAAO;wBACT;oBACF;oBACA,YAAY,YAAY,CAAC;wBAAC,qJAAoB;wBAAE;qBAAO,EAAE;gBAC3D;YACF;YAEA,wDAAwD;YACxD,MAAM,mBAAmB,yIAAS,CAAC,QAAQ,GAAG,eAAe,IAAI,EAAE;YACnE,MAAM,MAAM,iBAAiB,QAAQ,CAAC;YACtC,IAAI,eAAe;YACnB,IAAI,cAAc,CAAC,KAAK,eAAe;mBAAI;gBAAkB;aAAO;iBAC/D,IAAI,CAAC,cAAc,KACtB,eAAe,iBAAiB,MAAM,CAAC,CAAC,KAAO,OAAO;YACxD,yIAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YAEtC,OAAO;gBAAE;gBAAc,kBAAkB;YAAiB;QAC5D;QACA,SAAS,CACP,OACA,WACA;YAEA,QAAQ,KAAK,CAAC,kCAAkC;YAChD,iBAAiB;YACjB,IAAI,UAAU,WAAW,OAAO,YAAY,UAAU;gBACpD,MAAM,MAAM;gBAIZ,IAAI,IAAI,YAAY,EAAE;oBACpB,YAAY,YAAY,CACtB;wBAAC,qJAAoB;wBAAE;qBAAO,EAC9B,IAAI,YAAY;gBAEpB;gBACA,IAAI,IAAI,gBAAgB,EAAE;oBACxB,yIAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,gBAAgB;gBAC5D;YACF;QACF;QACA,WAAW;YACT,4DAA4D;YAC5D,IAAI,QAAQ;gBACV,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC,qJAAoB;wBAAE;qBAAO;gBAC1C;YACF;QACF;IACF;AACF;AAEO,MAAM,uBAAuB;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,qBAAqB,IAAA,6LAAW,EAIpC;QACA,YAAY,OAAO;YACjB,MAAM,YAAY,IAAI,sIAAS,CAC7B,CAAC,MAAM,EAAE,OAAO,mBAAmB,CAAC;YAEtC,OAAO,UAAU,IAAI,CAAC;QACxB;QACA,WAAW,CAAC,MAAM;YAChB,wCAAwC;YACxC,IAAI,UAAU,UAAU,EAAE;YACxB,uEAAuE;YACzE,OAAO,IAAI,UAAU,QAAQ,EAAE;gBAC7B,yIAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC,UAAU,QAAQ;YAC1D;YAEA,0CAA0C;YAC1C,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,qJAAoB;oBAAE;iBAAO;YAC1C;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,uBAAuB,IAAA,6LAAW,EAItC;QACA,YAAY,OAAO;YACjB,MAAM,YAAY,IAAI,sIAAS,CAC7B,CAAC,MAAM,EAAE,OAAO,qBAAqB,CAAC;YAExC,OAAO,UAAU,IAAI,CAAC;QACxB;QACA,WAAW,CAAC,MAAM;YAChB,wCAAwC;YACxC,IAAI,UAAU,YAAY,EAAE;gBAC1B,yIAAS,CAAC,QAAQ,GAAG,cAAc;YACrC,OAAO,IAAI,UAAU,QAAQ,EAAE;gBAC7B,uCAAuC;gBACvC,MAAM,mBAAmB,yIAAS,CAAC,QAAQ,GAAG,eAAe,IAAI,EAAE;gBACnE,MAAM,eAAe,iBAAiB,MAAM,CAC1C,CAAC,KAAO,CAAC,UAAU,QAAQ,EAAE,SAAS;gBAExC,yIAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YACxC;YAEA,0CAA0C;YAC1C,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,qJAAoB;oBAAE;iBAAO;YAC1C;QACF;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,OAAO;QACL,YAAY,mBAAmB,MAAM;QACrC,cAAc,qBAAqB,MAAM;QACzC,iBAAiB,mBAAmB,SAAS;QAC7C,mBAAmB,qBAAqB,SAAS;QACjD,aAAa,mBAAmB,KAAK;QACrC,eAAe,qBAAqB,KAAK;IAC3C;AACF;AAEO,MAAM,yBAAyB;IACpC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAE5B,MAAM,eAAe,IAAA,oNAAW,EAAC;QAC/B,MAAM,YAAY,IAAI,sIAAS,CAO5B,CAAC,MAAM,EAAE,OAAO,kBAAkB,CAAC;QAEtC,OAAO,UAAU,GAAG;IACtB,GAAG;QAAC;KAAO;IAEX,OAAO;QAAE;IAAa;AACxB,EAEA,qDAAqD;CACrD,8CAA8C;CAC9C,uFAAuF;CACvF,yEAAyE;CACzE,2CAA2C;CAC3C,iDAAiD;CAEjD,yCAAyC;CACzC,qEAAqE;CACrE,wCAAwC;CACxC,6CAA6C;CAC7C,8CAA8C;CAC9C,kCAAkC;CAElC,mGAAmG;CACnG,sCAAsC;CACtC,aAAa;CACb,0DAA0D;CAC1D,iEAAiE;CACjE,YAAY;CACZ,kFAAkF;CAClF,iBAAiB;CACjB,UAAU;CAEV,mEAAmE;CACnE,wCAAwC;CACxC,SAAS;CACT,SAAS;CACT,MAAM;CAEN,+BAA+B;CAC/B,IAAI", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart_item\": \"CartItemsList-module-scss-module__kultEW__cart_item\",\n  \"cart_item__extra_data\": \"CartItemsList-module-scss-module__kultEW__cart_item__extra_data\",\n  \"cart_item__img\": \"CartItemsList-module-scss-module__kultEW__cart_item__img\",\n  \"cart_item__info\": \"CartItemsList-module-scss-module__kultEW__cart_item__info\",\n  \"cart_item__quantity\": \"CartItemsList-module-scss-module__kultEW__cart_item__quantity\",\n  \"cart_item__title\": \"CartItemsList-module-scss-module__kultEW__cart_item__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/cart/CartItemsList.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\n\r\nimport { <PERSON><PERSON><PERSON>, FiMinus, FiTrash2 } from 'react-icons/fi'\r\nimport Image from 'next/image'\r\nimport Link from 'next/link'\r\nimport LimitTitleLength from '@/src/components/utils/TextLimit'\r\nimport { CartItemShape } from '@/src/types/store-types'\r\nimport {\r\n  useCartItemSelection,\r\n  useBulkCartSelection,\r\n} from '@/src/hooks/cart-selection-hooks'\r\nimport cartStore from '@/src/stores/cart-store'\r\nimport styles from './CartItemsList.module.scss'\r\n\r\ninterface CartItemsListProps {\r\n  cartItems: CartItemShape[]\r\n  handleIncrement?: (item: CartItemShape) => void\r\n  handleDecrement?: (item: CartItemShape) => void\r\n  deleteCartItem?: (itemId: number) => void\r\n  showSelection?: boolean\r\n  // optional server-provided selection set (server as source-of-truth)\r\n  selectedIds?: Set<number>\r\n}\r\n\r\nconst CartItemsList = ({\r\n  cartItems,\r\n  handleIncrement,\r\n  handleDecrement,\r\n  deleteCartItem,\r\n  showSelection = true,\r\n  selectedIds,\r\n}: CartItemsListProps) => {\r\n  // local persisted array (fallback when server doesn't provide selection)\r\n  const storeSelectedItemIds = cartStore((s) => s.selectedItemIds)\r\n  const toggleSelectAll = cartStore((s) => s.toggleSelectAll)\r\n  const { mutate: updateItemSelection } = useCartItemSelection()\r\n  const { bulkSelect, bulkDeselect } = useBulkCartSelection()\r\n\r\n  // Build a local Set for O(1) lookups from persisted array\r\n  // Prefer server-provided selection when available, otherwise use persisted array\r\n  const safeSelectedCartItems = React.useMemo(() => {\r\n    if (selectedIds && selectedIds instanceof Set)\r\n      return new Set<number>(selectedIds)\r\n    return new Set<number>(\r\n      Array.isArray(storeSelectedItemIds) ? storeSelectedItemIds : []\r\n    )\r\n  }, [selectedIds, storeSelectedItemIds])\r\n\r\n  const handleItemSelectionChange = (\r\n    itemId: number,\r\n    currentlySelected: boolean\r\n  ) => {\r\n    // Update backend\r\n    updateItemSelection({\r\n      itemId,\r\n      isSelected: !currentlySelected,\r\n    })\r\n  }\r\n\r\n  const handleSelectAllChange = () => {\r\n    const allItemIds = cartItems.map((item) => item.id)\r\n    const allSelected = allItemIds.every((id) => safeSelectedCartItems.has(id))\r\n\r\n    if (allSelected) {\r\n      // Deselect all\r\n      bulkDeselect({ deselect_all: true })\r\n      // when server is source-of-truth we avoid mutating local store\r\n      if (!selectedIds) toggleSelectAll(allItemIds)\r\n    } else {\r\n      // Select all\r\n      bulkSelect({ select_all: true, item_ids: allItemIds })\r\n      if (!selectedIds) toggleSelectAll(allItemIds)\r\n    }\r\n  }\r\n\r\n  const allItemsSelected =\r\n    cartItems.length > 0 &&\r\n    cartItems.every((item) => safeSelectedCartItems.has(item.id))\r\n  const someItemsSelected = cartItems.some((item) =>\r\n    safeSelectedCartItems.has(item.id)\r\n  )\r\n\r\n  return (\r\n    <div>\r\n      {showSelection && cartItems.length > 0 && (\r\n        <div className={styles.selection_header}>\r\n          <label className={styles.select_all_container}>\r\n            <input\r\n              type='checkbox'\r\n              checked={allItemsSelected}\r\n              ref={(input) => {\r\n                if (input)\r\n                  input.indeterminate = someItemsSelected && !allItemsSelected\r\n              }}\r\n              onChange={handleSelectAllChange}\r\n              className={styles.select_all_checkbox}\r\n            />\r\n            <span>Select All ({cartItems.length} items)</span>\r\n          </label>\r\n        </div>\r\n      )}\r\n\r\n      <ul>\r\n        {cartItems?.map((item: CartItemShape) => {\r\n          const isSelected = safeSelectedCartItems.has(item.id)\r\n\r\n          return (\r\n            <li\r\n              key={item.id}\r\n              className={`${styles.cart_item} ${\r\n                isSelected ? styles.selected : ''\r\n              }`}\r\n            >\r\n              {showSelection && (\r\n                <div className={styles.cart_item__selection}>\r\n                  <input\r\n                    type='checkbox'\r\n                    checked={isSelected}\r\n                    onChange={() =>\r\n                      handleItemSelectionChange(item.id, isSelected)\r\n                    }\r\n                    className={styles.item_checkbox}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              <div className={styles.cart_item__img}>\r\n                <Image\r\n                  src={\r\n                    item.product_variant?.product_image?.[0]?.image\r\n                      ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`\r\n                      : ''\r\n                  }\r\n                  alt={\r\n                    item.product_variant?.product_image?.[0]\r\n                      ?.alternative_text || item.product.title\r\n                  }\r\n                  width={200}\r\n                  height={200}\r\n                />\r\n              </div>\r\n\r\n              <div className={styles.cart_item__info}>\r\n                <span className={styles.cart_item__title}>\r\n                  <Link href={`/product/${item.product.slug}`}>\r\n                    <LimitTitleLength\r\n                      title={item.product.title}\r\n                      maxLength={60}\r\n                    />\r\n                  </Link>\r\n                </span>\r\n                {` `}\r\n                <span>\r\n                  (${item.product_variant.price} x {item.quantity})\r\n                </span>\r\n                <span>\r\n                  Variant: {item.product_variant?.price_label?.attribute_value}\r\n                </span>\r\n                {Object.entries(item.extra_data).map(([key, value], index) => (\r\n                  <div key={index} className={styles.cart_item__extra_data}>\r\n                    <p>{key} :</p>\r\n                    <p>{value}</p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className={styles.cart_item__quantity}>\r\n                <div>\r\n                  <p>Qty:</p>\r\n                  {handleDecrement && (\r\n                    <button\r\n                      onClick={() => handleDecrement(item)}\r\n                      disabled={item.product_variant.stock_qty === 0}\r\n                    >\r\n                      <i>\r\n                        <FiMinus />\r\n                      </i>\r\n                    </button>\r\n                  )}\r\n                  <p>{item.quantity}</p>\r\n                  {handleIncrement && (\r\n                    <button\r\n                      onClick={() => handleIncrement(item)}\r\n                      disabled={item.product_variant.stock_qty === 0}\r\n                    >\r\n                      <i>\r\n                        <FiPlus />\r\n                      </i>\r\n                    </button>\r\n                  )}\r\n                  {deleteCartItem && (\r\n                    <button onClick={() => deleteCartItem(item.id)}>\r\n                      <i>\r\n                        <FiTrash2 />\r\n                      </i>\r\n                    </button>\r\n                  )}\r\n                </div>\r\n                {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}\r\n              </div>\r\n            </li>\r\n          )\r\n        })}\r\n      </ul>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CartItemsList\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AAEA;AAIA;AACA;AAdA;;;;;;;;;;AA0BA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,eAAe,EACf,eAAe,EACf,cAAc,EACd,gBAAgB,IAAI,EACpB,WAAW,EACQ;IACnB,yEAAyE;IACzE,MAAM,uBAAuB,IAAA,yIAAS,EAAC,CAAC,IAAM,EAAE,eAAe;IAC/D,MAAM,kBAAkB,IAAA,yIAAS,EAAC,CAAC,IAAM,EAAE,eAAe;IAC1D,MAAM,EAAE,QAAQ,mBAAmB,EAAE,GAAG,IAAA,kKAAoB;IAC5D,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,IAAA,kKAAoB;IAEzD,0DAA0D;IAC1D,iFAAiF;IACjF,MAAM,wBAAwB,gNAAK,CAAC,OAAO,CAAC;QAC1C,IAAI,eAAe,uBAAuB,KACxC,OAAO,IAAI,IAAY;QACzB,OAAO,IAAI,IACT,MAAM,OAAO,CAAC,wBAAwB,uBAAuB,EAAE;IAEnE,GAAG;QAAC;QAAa;KAAqB;IAEtC,MAAM,4BAA4B,CAChC,QACA;QAEA,iBAAiB;QACjB,oBAAoB;YAClB;YACA,YAAY,CAAC;QACf;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,aAAa,UAAU,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;QAClD,MAAM,cAAc,WAAW,KAAK,CAAC,CAAC,KAAO,sBAAsB,GAAG,CAAC;QAEvE,IAAI,aAAa;YACf,eAAe;YACf,aAAa;gBAAE,cAAc;YAAK;YAClC,+DAA+D;YAC/D,IAAI,CAAC,aAAa,gBAAgB;QACpC,OAAO;YACL,aAAa;YACb,WAAW;gBAAE,YAAY;gBAAM,UAAU;YAAW;YACpD,IAAI,CAAC,aAAa,gBAAgB;QACpC;IACF;IAEA,MAAM,mBACJ,UAAU,MAAM,GAAG,KACnB,UAAU,KAAK,CAAC,CAAC,OAAS,sBAAsB,GAAG,CAAC,KAAK,EAAE;IAC7D,MAAM,oBAAoB,UAAU,IAAI,CAAC,CAAC,OACxC,sBAAsB,GAAG,CAAC,KAAK,EAAE;IAGnC,qBACE,8OAAC;;YACE,iBAAiB,UAAU,MAAM,GAAG,mBACnC,8OAAC;gBAAI,WAAW,yMAAM,CAAC,gBAAgB;0BACrC,cAAA,8OAAC;oBAAM,WAAW,yMAAM,CAAC,oBAAoB;;sCAC3C,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,KAAK,CAAC;gCACJ,IAAI,OACF,MAAM,aAAa,GAAG,qBAAqB,CAAC;4BAChD;4BACA,UAAU;4BACV,WAAW,yMAAM,CAAC,mBAAmB;;;;;;sCAEvC,8OAAC;;gCAAK;gCAAa,UAAU,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC;0BACE,WAAW,IAAI,CAAC;oBACf,MAAM,aAAa,sBAAsB,GAAG,CAAC,KAAK,EAAE;oBAEpD,qBACE,8OAAC;wBAEC,WAAW,GAAG,yMAAM,CAAC,SAAS,CAAC,CAAC,EAC9B,aAAa,yMAAM,CAAC,QAAQ,GAAG,IAC/B;;4BAED,+BACC,8OAAC;gCAAI,WAAW,yMAAM,CAAC,oBAAoB;0CACzC,cAAA,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,IACR,0BAA0B,KAAK,EAAE,EAAE;oCAErC,WAAW,yMAAM,CAAC,aAAa;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAW,yMAAM,CAAC,cAAc;0CACnC,cAAA,8OAAC,wIAAK;oCACJ,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,QACtC,yEAA0C,CAAC,EAAE,KAAK,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAC1F;oCAEN,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EACpC,oBAAoB,KAAK,OAAO,CAAC,KAAK;oCAE5C,OAAO;oCACP,QAAQ;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAW,yMAAM,CAAC,eAAe;;kDACpC,8OAAC;wCAAK,WAAW,yMAAM,CAAC,gBAAgB;kDACtC,cAAA,8OAAC,uKAAI;4CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE;sDACzC,cAAA,8OAAC,mJAAgB;gDACf,OAAO,KAAK,OAAO,CAAC,KAAK;gDACzB,WAAW;;;;;;;;;;;;;;;;oCAIhB,CAAC,CAAC,CAAC;kDACJ,8OAAC;;4CAAK;4CACD,KAAK,eAAe,CAAC,KAAK;4CAAC;4CAAI,KAAK,QAAQ;4CAAC;;;;;;;kDAElD,8OAAC;;4CAAK;4CACM,KAAK,eAAe,EAAE,aAAa;;;;;;;oCAE9C,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,sBAClD,8OAAC;4CAAgB,WAAW,yMAAM,CAAC,qBAAqB;;8DACtD,8OAAC;;wDAAG;wDAAI;;;;;;;8DACR,8OAAC;8DAAG;;;;;;;2CAFI;;;;;;;;;;;0CAOd,8OAAC;gCAAI,WAAW,yMAAM,CAAC,mBAAmB;;kDACxC,8OAAC;;0DACC,8OAAC;0DAAE;;;;;;4CACF,iCACC,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;0DAE7C,cAAA,8OAAC;8DACC,cAAA,8OAAC,yJAAO;;;;;;;;;;;;;;;0DAId,8OAAC;0DAAG,KAAK,QAAQ;;;;;;4CAChB,iCACC,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;0DAE7C,cAAA,8OAAC;8DACC,cAAA,8OAAC,wJAAM;;;;;;;;;;;;;;;4CAIZ,gCACC,8OAAC;gDAAO,SAAS,IAAM,eAAe,KAAK,EAAE;0DAC3C,cAAA,8OAAC;8DACC,cAAA,8OAAC,0JAAQ;;;;;;;;;;;;;;;;;;;;;oCAKhB,KAAK,eAAe,CAAC,SAAS,KAAK,mBAAK,8OAAC;kDAAE;;;;;;;;;;;;;uBA1FzC,KAAK,EAAE;;;;;gBA8FlB;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart__checkout\": \"PriceSummary-module-scss-module__7p8iVa__cart__checkout\",\n  \"total\": \"PriceSummary-module-scss-module__7p8iVa__total\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/tooltip/Tooltip.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"tooltip\": \"Tooltip-module-scss-module__vdbe-W__tooltip\",\n  \"tooltip--bottom\": \"Tooltip-module-scss-module__vdbe-W__tooltip--bottom\",\n  \"tooltip--condition\": \"Tooltip-module-scss-module__vdbe-W__tooltip--condition\",\n  \"tooltip--hover\": \"Tooltip-module-scss-module__vdbe-W__tooltip--hover\",\n  \"tooltip--left\": \"Tooltip-module-scss-module__vdbe-W__tooltip--left\",\n  \"tooltip--right\": \"Tooltip-module-scss-module__vdbe-W__tooltip--right\",\n  \"tooltip--top\": \"Tooltip-module-scss-module__vdbe-W__tooltip--top\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/tooltip/Tooltip.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react'\nimport styles from './Tooltip.module.scss'\n\ninterface TooltipProps {\n  children: ReactNode\n  content: string\n  position?: 'top' | 'bottom' | 'left' | 'right'\n  disabled?: boolean\n  className?: string\n  showOnHover?: boolean\n  showOnCondition?: boolean\n}\n\nconst Tooltip: React.FC<TooltipProps> = ({\n  children,\n  content,\n  position = 'top',\n  disabled = false,\n  className = '',\n  showOnHover = true,\n  showOnCondition = false\n}) => {\n  // Don't show tooltip if disabled or no content\n  if (disabled || !content) {\n    return <>{children}</>\n  }\n\n  const tooltipClasses = [\n    styles.tooltip,\n    styles[`tooltip--${position}`],\n    showOnHover ? styles['tooltip--hover'] : '',\n    showOnCondition ? styles['tooltip--condition'] : '',\n    className\n  ].filter(Boolean).join(' ')\n\n  return (\n    <span className={tooltipClasses} data-tooltip={content}>\n      {children}\n    </span>\n  )\n}\n\nexport default Tooltip\n"], "names": [], "mappings": ";;;;;AACA;;;AAYA,MAAM,UAAkC,CAAC,EACvC,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,kBAAkB,KAAK,EACxB;IACC,+CAA+C;IAC/C,IAAI,YAAY,CAAC,SAAS;QACxB,qBAAO;sBAAG;;IACZ;IAEA,MAAM,iBAAiB;QACrB,wKAAM,CAAC,OAAO;QACd,wKAAM,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC;QAC9B,cAAc,wKAAM,CAAC,iBAAiB,GAAG;QACzC,kBAAkB,wKAAM,CAAC,qBAAqB,GAAG;QACjD;KACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QAAK,WAAW;QAAgB,gBAAc;kBAC5C;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/price-summary/PriceSummary.tsx"], "sourcesContent": ["import React from 'react'\r\nimport { RiQuestionFill } from 'react-icons/ri'\r\nimport styles from './PriceSummary.module.scss'\r\nimport Tooltip from '@/src/components/utils/tooltip/Tooltip'\r\n\r\ninterface PriceSummaryProps {\r\n  totalPrice: number\r\n  shippingCost?: number\r\n  packingCost?: number\r\n  grandTotal?: number\r\n  item_count: number\r\n  cart_weight: number\r\n  onCheckout?: () => void\r\n  isShippingCalculated?: boolean\r\n}\r\n\r\nconst PriceSummary: React.FC<PriceSummaryProps> = ({\r\n  totalPrice,\r\n  shippingCost,\r\n  packingCost,\r\n  grandTotal,\r\n  item_count,\r\n  cart_weight,\r\n  onCheckout,\r\n  isShippingCalculated = false,\r\n}) => {\r\n  return (\r\n    <div className={styles.cart__checkout}>\r\n      <div>\r\n        <p>Item count: </p> <p>{item_count}</p>\r\n      </div>\r\n\r\n      <div>\r\n        <p>\r\n          Cart weight:\r\n          <Tooltip\r\n            content={`Shipping cost will be calculated after finalizing adding items to the cart.`}\r\n            position='top'\r\n          >\r\n            <i>\r\n              <RiQuestionFill />\r\n            </i>\r\n          </Tooltip>\r\n        </p>\r\n        <p>{cart_weight}g</p>\r\n      </div>\r\n\r\n      <div>\r\n        <p>Item cost: </p> <p>${totalPrice.toFixed(2)}</p>\r\n      </div>\r\n\r\n      {isShippingCalculated && shippingCost !== undefined && (\r\n        <div>\r\n          <p>Shipping cost: </p> <p>${shippingCost.toFixed(2)}</p>\r\n        </div>\r\n      )}\r\n\r\n      {isShippingCalculated && packingCost !== undefined && packingCost > 0 && (\r\n        <div>\r\n          <p>Packing cost: </p> <p>${packingCost.toFixed(2)}</p>\r\n        </div>\r\n      )}\r\n\r\n      {isShippingCalculated && grandTotal !== undefined && (\r\n        <div className={styles.total}>\r\n          <p>\r\n            <strong>Total: </strong>\r\n          </p>{' '}\r\n          <p>\r\n            <strong>${grandTotal.toFixed(2)}</strong>\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PriceSummary\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAaA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,YAAY,EACZ,WAAW,EACX,UAAU,EACV,UAAU,EACV,WAAW,EACX,UAAU,EACV,uBAAuB,KAAK,EAC7B;IACC,qBACE,8OAAC;QAAI,WAAW,oNAAM,CAAC,cAAc;;0BACnC,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAgB;kCAAC,8OAAC;kCAAG;;;;;;;;;;;;0BAG1B,8OAAC;;kCACC,8OAAC;;4BAAE;0CAED,8OAAC,4JAAO;gCACN,SAAS,CAAC,2EAA2E,CAAC;gCACtF,UAAS;0CAET,cAAA,8OAAC;8CACC,cAAA,8OAAC,gKAAc;;;;;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;;4BAAG;4BAAY;;;;;;;;;;;;;0BAGlB,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAe;kCAAC,8OAAC;;4BAAE;4BAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;YAG5C,wBAAwB,iBAAiB,2BACxC,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAmB;kCAAC,8OAAC;;4BAAE;4BAAE,aAAa,OAAO,CAAC;;;;;;;;;;;;;YAIpD,wBAAwB,gBAAgB,aAAa,cAAc,mBAClE,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAkB;kCAAC,8OAAC;;4BAAE;4BAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;YAIlD,wBAAwB,eAAe,2BACtC,8OAAC;gBAAI,WAAW,oNAAM,CAAC,KAAK;;kCAC1B,8OAAC;kCACC,cAAA,8OAAC;sCAAO;;;;;;;;;;;oBACL;kCACL,8OAAC;kCACC,cAAA,8OAAC;;gCAAO;gCAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;YAKlC,4BAAc,8OAAC;gBAAO,SAAS;0BAAY;;;;;;;;;;;;AAGlD;uCAEe", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/checkout/address-choice/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport styles from './AddressStage.module.scss'\nimport authStore from '@/src/stores/auth-store'\nimport { useCustomerDetails } from '@/src/hooks/customer-hooks'\nimport cartStore from '@/src/stores/cart-store'\nimport {\n  useCart,\n  useDeleteCartItem,\n  useUpdateCartCustomer,\n} from '@/src/hooks/cart-hooks'\nimport { AddressFormInputs } from '@/src/components/account/addresses/ManageAddresses'\nimport EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'\nimport Alert from '@/src/components/utils/alert/Alert'\nimport Logo from '@/src/components/utils/logo/Logo'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport Link from 'next/link'\nimport CartItemsList from '../../components/cart/CartItemsList'\nimport PriceSummary from '../../components/price-summary/PriceSummary'\n\nconst AddressChoice = () => {\n  const router = useRouter()\n\n  const { isLoggedIn } = authStore()\n  const { data: customer } = useCustomerDetails(isLoggedIn)\n  const { cartId, setSelectedAddress, selectedAddress } = cartStore()\n  const { isLoading, error, data } = useCart()\n  const { mutate: deleteCartItem } = useDeleteCartItem()\n  const { updateCartCustomer, isPending: isUpdatingCart } =\n    useUpdateCartCustomer()\n\n  const [addressesReady, setAddressesReady] = useState(false)\n  const [cartUpdated, setCartUpdated] = useState(false)\n\n  useEffect(() => {\n    if (!isLoggedIn) {\n      router.push('/login')\n    }\n  }, [isLoggedIn, router])\n\n  // Update cart with authenticated customer when page loads\n  // Update cart with authenticated customer when page loads\n  useEffect(() => {\n    if (\n      isLoggedIn &&\n      cartId &&\n      customer &&\n      !cartUpdated &&\n      !isUpdatingCart &&\n      !data?.customer // <-- only if cart has no customer yet\n    ) {\n      updateCartCustomer({})\n      setCartUpdated(true)\n    }\n  }, [\n    isLoggedIn,\n    cartId,\n    customer,\n    cartUpdated,\n    isUpdatingCart,\n    updateCartCustomer,\n    data?.customer, // <-- add to dependency array\n  ])\n\n  useEffect(() => {\n    if (customer?.address && customer.address.length > 0) {\n      if (!selectedAddress || Object.keys(selectedAddress).length === 0) {\n        setSelectedAddress(customer.address[0])\n      }\n      setAddressesReady(true)\n    }\n  }, [customer, selectedAddress, setSelectedAddress])\n\n  // Handle out-of-stock items by removing them\n  useEffect(() => {\n    if (data && data?.cart_items?.length > 0) {\n      const outOfStockItems = data.cart_items.filter(\n        (item) => item.product_variant.stock_qty === 0\n      )\n\n      if (outOfStockItems.length > 0) {\n        outOfStockItems.forEach((item) => {\n          deleteCartItem(item.id) // Remove each out-of-stock item from the cart\n        })\n      }\n    }\n  }, [data, deleteCartItem])\n\n  const handleAddressChange = (address: AddressFormInputs) => {\n    setSelectedAddress(address)\n  }\n\n  return (\n    <>\n      {!cartId ? (\n        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />\n      ) : (\n        <>\n          {isLoading ? (\n            <Spinner color='#0091CF' size={20} loading />\n          ) : (\n            <>\n              {error ? (\n                <Alert variant='error' message={error.message} />\n              ) : (\n                <>\n                  {customer?.address?.length === 0 ||\n                  customer?.phone_number === '' ? (\n                    <>\n                      <div className='logo_header'>\n                        <Logo />\n                      </div>\n                      <div className={styles.missing_addresses}>\n                        <Alert\n                          variant='warning'\n                          textSize='20'\n                          message=\"\n                        You haven't added a shipping address yet. \n                        Please add one along with a phone number to continue with checkout. Thank you! 😊\"\n                        />\n                        <section className='btn_container'>\n                          <button\n                            className='empty_btn'\n                            onClick={() => router.push('/account/profile')}\n                          >\n                            Update Profile\n                          </button>\n                        </section>\n                      </div>\n                    </>\n                  ) : (\n                    <>\n                      {!data || data.cart_items.length === 0 ? (\n                        <div className={styles.empty_cart}>\n                          <p>\n                            Your cart is empty. Add some products to the cart to\n                            checkout!\n                          </p>\n                          <Link href='/'>Go Shopping </Link>\n                        </div>\n                      ) : (\n                        <>\n                          <section>\n                            <section\n                              className={`container ${styles.address_stage}`}\n                            >\n                              <h3>Address Choices</h3>\n                              <div className={styles.contact_details}>\n                                <h3>Contact Details: </h3>\n                                <p>\n                                  Deliver to: {customer?.first_name}{' '}\n                                  {customer?.last_name}\n                                </p>\n                                <p>Phone: {customer?.phone_number}</p>\n                                <p>Email to: {customer?.email}</p>\n                              </div>\n                              <hr />\n                              <div className={styles.cart}>\n                                <CartItemsList cartItems={data.cart_items} />\n                                <PriceSummary\n                                  totalPrice={data?.total_price}\n                                  // shippingCost={data?.shipping_cost}\n                                  // grandTotal={data?.grand_total}\n                                  item_count={data?.item_count}\n                                  cart_weight={data?.cart_weight}\n                                />\n                              </div>\n                              <hr />\n                              {/* Render the addresses only when addresses are ready */}\n                              {addressesReady && (\n                                <div className={styles.address_selection}>\n                                  <h3>Choose a shipping address: </h3>\n                                  {customer?.address?.map((address) => (\n                                    <address key={address.id}>\n                                      <input\n                                        type='radio'\n                                        id={`address-${address.id}`}\n                                        name='address'\n                                        value={address.id}\n                                        checked={\n                                          selectedAddress?.id === address.id\n                                        }\n                                        onChange={() =>\n                                          handleAddressChange(address)\n                                        }\n                                      />\n                                      <label htmlFor={`address-${address.id}`}>\n                                        {address.full_name},{' '}\n                                        {address.street_name},{' '}\n                                        {address.city_or_village}\n                                      </label>\n                                    </address>\n                                  ))}\n                                  <button\n                                    onClick={() =>\n                                      router.push('/checkout/payment-choice')\n                                    }\n                                  >\n                                    Use this address\n                                  </button>\n                                </div>\n                              )}\n                              <hr />\n                            </section>\n                          </section>\n                        </>\n                      )}\n                    </>\n                  )}\n                </>\n              )}\n            </>\n          )}\n        </>\n      )}\n    </>\n  )\n}\n\nexport default AddressChoice\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;;;;;;AAsBA,MAAM,gBAAgB;IACpB,MAAM,SAAS,IAAA,+IAAS;IAExB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,yIAAS;IAChC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,IAAA,uJAAkB,EAAC;IAC9C,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG,IAAA,yIAAS;IACjE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,wIAAO;IAC1C,MAAM,EAAE,QAAQ,cAAc,EAAE,GAAG,IAAA,kJAAiB;IACpD,MAAM,EAAE,kBAAkB,EAAE,WAAW,cAAc,EAAE,GACrD,IAAA,sJAAqB;IAEvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAE/C,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,0DAA0D;IAC1D,0DAA0D;IAC1D,IAAA,kNAAS,EAAC;QACR,IACE,cACA,UACA,YACA,CAAC,eACD,CAAC,kBACD,CAAC,MAAM,SAAS,uCAAuC;UACvD;YACA,mBAAmB,CAAC;YACpB,eAAe;QACjB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,MAAM;KACP;IAED,IAAA,kNAAS,EAAC;QACR,IAAI,UAAU,WAAW,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG;YACpD,IAAI,CAAC,mBAAmB,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,GAAG;gBACjE,mBAAmB,SAAS,OAAO,CAAC,EAAE;YACxC;YACA,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAU;QAAiB;KAAmB;IAElD,6CAA6C;IAC7C,IAAA,kNAAS,EAAC;QACR,IAAI,QAAQ,MAAM,YAAY,SAAS,GAAG;YACxC,MAAM,kBAAkB,KAAK,UAAU,CAAC,MAAM,CAC5C,CAAC,OAAS,KAAK,eAAe,CAAC,SAAS,KAAK;YAG/C,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,gBAAgB,OAAO,CAAC,CAAC;oBACvB,eAAe,KAAK,EAAE,GAAE,8CAA8C;gBACxE;YACF;QACF;IACF,GAAG;QAAC;QAAM;KAAe;IAEzB,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;IACrB;IAEA,qBACE;kBACG,CAAC,uBACA,8OAAC,oKAAS;YAAC,SAAQ;;;;;qEAEnB;sBACG,0BACC,8OAAC,4JAAO;gBAAC,OAAM;gBAAU,MAAM;gBAAI,OAAO;;;;;yEAE1C;0BACG,sBACC,8OAAC,wJAAK;oBAAC,SAAQ;oBAAQ,SAAS,MAAM,OAAO;;;;;6EAE7C;8BACG,UAAU,SAAS,WAAW,KAC/B,UAAU,iBAAiB,mBACzB;;0CACE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sJAAI;;;;;;;;;;0CAEP,8OAAC;gCAAI,WAAW,mNAAM,CAAC,iBAAiB;;kDACtC,8OAAC,wJAAK;wCACJ,SAAQ;wCACR,UAAS;wCACT,SAAQ;;;;;;kDAIV,8OAAC;wCAAQ,WAAU;kDACjB,cAAA,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;;;;;;;qDAOP;kCACG,CAAC,QAAQ,KAAK,UAAU,CAAC,MAAM,KAAK,kBACnC,8OAAC;4BAAI,WAAW,mNAAM,CAAC,UAAU;;8CAC/B,8OAAC;8CAAE;;;;;;8CAIH,8OAAC,uKAAI;oCAAC,MAAK;8CAAI;;;;;;;;;;;qFAGjB;sCACE,cAAA,8OAAC;0CACC,cAAA,8OAAC;oCACC,WAAW,CAAC,UAAU,EAAE,mNAAM,CAAC,aAAa,EAAE;;sDAE9C,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;4CAAI,WAAW,mNAAM,CAAC,eAAe;;8DACpC,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;;wDAAE;wDACY,UAAU;wDAAY;wDAClC,UAAU;;;;;;;8DAEb,8OAAC;;wDAAE;wDAAQ,UAAU;;;;;;;8DACrB,8OAAC;;wDAAE;wDAAW,UAAU;;;;;;;;;;;;;sDAE1B,8OAAC;;;;;sDACD,8OAAC;4CAAI,WAAW,mNAAM,CAAC,IAAI;;8DACzB,8OAAC,6LAAa;oDAAC,WAAW,KAAK,UAAU;;;;;;8DACzC,8OAAC,wMAAY;oDACX,YAAY,MAAM;oDAClB,qCAAqC;oDACrC,iCAAiC;oDACjC,YAAY,MAAM;oDAClB,aAAa,MAAM;;;;;;;;;;;;sDAGvB,8OAAC;;;;;wCAEA,gCACC,8OAAC;4CAAI,WAAW,mNAAM,CAAC,iBAAiB;;8DACtC,8OAAC;8DAAG;;;;;;gDACH,UAAU,SAAS,IAAI,CAAC,wBACvB,8OAAC;;0EACC,8OAAC;gEACC,MAAK;gEACL,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;gEAC3B,MAAK;gEACL,OAAO,QAAQ,EAAE;gEACjB,SACE,iBAAiB,OAAO,QAAQ,EAAE;gEAEpC,UAAU,IACR,oBAAoB;;;;;;0EAGxB,8OAAC;gEAAM,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;;oEACpC,QAAQ,SAAS;oEAAC;oEAAE;oEACpB,QAAQ,WAAW;oEAAC;oEAAE;oEACtB,QAAQ,eAAe;;;;;;;;uDAhBd,QAAQ,EAAE;;;;;8DAoB1B,8OAAC;oDACC,SAAS,IACP,OAAO,IAAI,CAAC;8DAEf;;;;;;;;;;;;sDAKL,8OAAC;;;;;;;;;;;;;;;;;;;;;;AAe/B;uCAEe", "debugId": null}}]}