{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(account)/account/profile/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(account)/account/profile/Profile.module.scss", "turbopack:///turbopack:///[project]/app/(account)/account/profile/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/app/(account)/account/profile/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../../src/scss/variables' as *;\r\n@use '../../../../src/scss/mixins' as *;\r\n\r\n// Mobile-first base styles\r\n.profile {\r\n  width: 100%;\r\n  @include flexbox(flex-start, center, column);\r\n  padding: 0 1rem;\r\n\r\n  .user_details {\r\n    width: 100%;\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n.profile__details {\r\n  @include flexbox(flex-start, flex-start, column);\r\n  gap: 1rem;\r\n  padding: 0.5rem;\r\n\r\n  div {\r\n    h5 {\r\n      font-weight: bold;\r\n      color: $primary-dark-text-color;\r\n      margin-bottom: 0.5rem;\r\n      font-size: 16px;\r\n    }\r\n\r\n    p {\r\n      color: $primary-lighter-text-color;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  button {\r\n    align-self: stretch;\r\n    margin-top: 1rem;\r\n  }\r\n}\r\n\r\n.profile__editable {\r\n  padding: 0.5rem;\r\n\r\n  form {\r\n    @include flexbox(flex-start, flex-start, column);\r\n    gap: 1rem;\r\n\r\n    div {\r\n      width: 100%;\r\n\r\n      label {\r\n        font-size: 14px;\r\n      }\r\n\r\n      input {\r\n        width: 100%;\r\n        padding: 0.7rem;\r\n      }\r\n    }\r\n\r\n    button {\r\n      width: 100%;\r\n      margin-top: 1rem;\r\n    }\r\n  }\r\n}\r\n\r\n.edit_btn {\r\n  @include btn($primary-blue, #fff);\r\n  padding: 0.3rem 1.2rem;\r\n  border: 1px solid $lighten-blue;\r\n  letter-spacing: 0.7px;\r\n  transition: all 0.3s ease;\r\n  height: fit-content;\r\n  font-size: 14px;\r\n\r\n  &:hover {\r\n    border: 1px solid $primary-dark-blue;\r\n    color: $primary-dark-blue;\r\n  }\r\n}\r\n\r\n.edit_btn__empty {\r\n  @include btn($lighten-blue, #fff);\r\n  padding: 0.3rem 1.2rem;\r\n  border: 1px solid $lighten-blue;\r\n  letter-spacing: 0.7px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border: 1px solid $primary-blue;\r\n    color: $primary-blue;\r\n  }\r\n}\r\n\r\n.reset_details {\r\n  row-gap: 1.5rem;\r\n  padding: 0.5rem;\r\n\r\n  .reset_details__section {\r\n    @include flexbox(flex-start, flex-start, column);\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n    background-color: #f9f9f9;\r\n    border-radius: 8px;\r\n\r\n    div {\r\n      width: 100%;\r\n\r\n      h5 {\r\n        font-weight: bold;\r\n        color: $primary-dark-text-color;\r\n        margin-bottom: 0.5rem;\r\n        font-size: 16px;\r\n      }\r\n\r\n      p {\r\n        color: $primary-lighter-text-color;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .no_data {\r\n      font-style: italic;\r\n    }\r\n\r\n    button {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n// Tablet and larger screens\r\n@media (min-width: $tablet) {\r\n  .profile {\r\n    padding: 0;\r\n\r\n    .user_details {\r\n      max-width: 600px;\r\n    }\r\n\r\n    h2 {\r\n      padding: 0.5rem 0;\r\n      font-size: 23px;\r\n    }\r\n  }\r\n\r\n  .profile__details {\r\n    @include flexbox(space-between, flex-start, row);\r\n    gap: 2rem;\r\n    padding: 1rem;\r\n\r\n    div {\r\n      h5 {\r\n        font-size: 17px;\r\n      }\r\n\r\n      p {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n\r\n    button {\r\n      align-self: center;\r\n      margin-top: 0;\r\n      max-width: fit-content;\r\n    }\r\n  }\r\n\r\n  .profile__editable {\r\n    padding: 1rem;\r\n\r\n    form {\r\n      @include flexbox(space-between, flex-start, row);\r\n      gap: 2rem;\r\n\r\n      div {\r\n        width: auto;\r\n\r\n        label {\r\n          font-size: 16px;\r\n        }\r\n\r\n        input {\r\n          width: auto;\r\n          padding: 0.5rem;\r\n        }\r\n      }\r\n\r\n      button {\r\n        width: auto;\r\n        margin-top: 0;\r\n        max-width: fit-content;\r\n        align-self: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .reset_details {\r\n    row-gap: 1rem;\r\n    padding: 1rem;\r\n\r\n    .reset_details__section {\r\n      @include flexbox(space-between, center, row);\r\n      gap: 2rem;\r\n      padding: 0;\r\n      background-color: transparent;\r\n      border-radius: 0;\r\n\r\n      div {\r\n        width: auto;\r\n\r\n        h5 {\r\n          font-size: 17px;\r\n        }\r\n\r\n        p {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      button {\r\n        width: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAIA;;;;;;;;;AAKE;;;;;AAMF;;;;;;;;;AAMI;;;;;;;AAOA;;;;;AAMF;;;;;AAMF;;;;AAGE;;;;;;;;AAIE;;;;AAGE;;;;AAIA;;;;;AAMF;;;;;AAOJ;;;;;;;;;;;;;;;;;AASE;;;;;AAMF;;;;;;;;;;;;;;AAOE;;;;;AAMF;;;;;AAIE;;;;;;;;;;;AAOE;;;;AAGE;;;;;;;AAOA;;;;;AAMF;;;;AAIA;;;;AAOJ;EACE;;;;EAGE;;;;EAIA;;;;;EAMF;;;;;;;;;EAMI;;;;EAIA;;;;EAKF;;;;;;;EAOF;;;;EAGE;;;;;;;;EAIE;;;;EAGE;;;;EAIA;;;;;EAMF;;;;;;;;EASJ;;;;;EAIE;;;;;;;;;;;EAOE;;;;EAGE;;;;EAIA;;;;EAKF"}}]}