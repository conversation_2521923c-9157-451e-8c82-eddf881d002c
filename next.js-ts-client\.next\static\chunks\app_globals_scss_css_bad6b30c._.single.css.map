{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/_reset.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_animations.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_common.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/globals.scss"], "sourcesContent": ["/* Modern CSS Reset */\r\n/* Box sizing rules */\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* Remove default margin and padding */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n/* Remove list styles on ul, ol elements with a list role */\r\nul[role=\"list\"],\r\nol[role=\"list\"] {\r\n  list-style: none;\r\n}\r\n\r\n/* Set core root defaults */\r\nhtml:focus-within {\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n/* Set core body defaults */\r\nbody {\r\n  min-height: 100vh;\r\n  text-rendering: optimizeSpeed;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* A elements that don't have a class get default styles */\r\na:not([class]) {\r\n  text-decoration-skip-ink: auto;\r\n}\r\n\r\n/* Make images easier to work with */\r\nimg,\r\npicture {\r\n  max-width: 100%;\r\n  display: block;\r\n}\r\n\r\n/* Inherit fonts for inputs and buttons */\r\ninput,\r\nbutton,\r\ntextarea,\r\nselect {\r\n  font: inherit;\r\n}\r\n\r\n/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */\r\n@media (prefers-reduced-motion: reduce) {\r\n  html:focus-within {\r\n    scroll-behavior: auto;\r\n  }\r\n  \r\n  *,\r\n  *::before,\r\n  *::after {\r\n    animation-duration: 0.01ms !important;\r\n    animation-iteration-count: 1 !important;\r\n    transition-duration: 0.01ms !important;\r\n    scroll-behavior: auto !important;\r\n  }\r\n}\r\n\r\n/* Additional reset for form elements */\r\nbutton {\r\n  border: none;\r\n  background: none;\r\n  cursor: pointer;\r\n}\r\n\r\ninput {\r\n  border: none;\r\n  outline: none;\r\n}\r\n\r\n/* Remove default button styles */\r\nbutton {\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  appearance: none;\r\n}\r\n\r\n/* Remove default link styles */\r\na {\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\n\r\n/* Ensure form elements inherit font */\r\ninput,\r\nbutton,\r\ntextarea,\r\nselect {\r\n  font-family: inherit;\r\n  font-size: inherit;\r\n}\r\n\r\n/* Remove default textarea resize */\r\ntextarea {\r\n  resize: vertical;\r\n}", "// Define the slideIn animation globally\r\n@keyframes slideIn {\r\n  0% {\r\n    transform: translateX(-50px);\r\n    /* Start from 50px left */\r\n    opacity: 0;\r\n    /* Fully transparent */\r\n  }\r\n\r\n  100% {\r\n    transform: translateX(0);\r\n    /* Move to natural position */\r\n    opacity: 1;\r\n    /* Fully visible */\r\n  }\r\n}\r\n\r\n// Optional: Create a mixin for reusable animation settings\r\n@mixin slideInAnimation($duration: 0.5s, $easing: ease-out) {\r\n  animation: slideIn $duration $easing;\r\n}", "@use './variables' as *;\r\n@use './mixins' as *;\r\n@use './animations' as *;\r\n@use 'sass:color';\r\n\r\na {\r\n  text-decoration: none;\r\n}\r\n\r\n.title {\r\n  font-size: $font-size-3;\r\n  font-weight: 600;\r\n  // margin-bottom: 1rem;\r\n  text-align: center;\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  // background-color: aqua;\r\n}\r\n\r\nhtml {\r\n  font-family: $primary-font-family;\r\n}\r\n\r\nbutton {\r\n  cursor: pointer;\r\n  border: none;\r\n  letter-spacing: 0.4px;\r\n  border-radius: 2px;\r\n  font-size: 16px;\r\n}\r\n\r\n.loading_svg {\r\n  margin: 2px 10px;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.logo_header {\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: $primary-dark;\r\n  padding: 10px 0;\r\n}\r\n\r\n.title {\r\n  font-size: 25px;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  margin: 1rem 0;\r\n  color: $primary-dark-blue;\r\n}\r\n\r\n.form {\r\n  .form_group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin: 15px 0;\r\n    row-gap: 4px;\r\n\r\n    .form_label {\r\n      font-weight: bold;\r\n      color: $primary-dark-blue;\r\n    }\r\n\r\n    .form_input {\r\n      width: 100%;\r\n      border: 0.1px solid $primary-dark-text-color;\r\n      border-radius: 3px;\r\n      padding: 5px 5px;\r\n      font-size: 16.5px;\r\n\r\n      &:focus {\r\n        outline: 2px solid $lighten-blue;\r\n        border: none;\r\n      }\r\n    }\r\n\r\n    .form_error {\r\n      color: $error-red;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.empty_btn {\r\n  @include btn($lighten-blue, #fff);\r\n  // margin: 0 auto 0 auto;\r\n  padding: 0.36rem 1.2rem;\r\n  border: 1px solid $lighten-blue;\r\n  letter-spacing: 0.7px;\r\n  transition: all 0.2s ease;\r\n\r\n  &:hover {\r\n    border: 1px solid $primary-blue;\r\n    // color: $primary-blue;\r\n  }\r\n}\r\n\r\n.delete_btn {\r\n  @include btn($error-red, #fff);\r\n  padding: 0.6rem;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background-color: color.adjust($error-red, $lightness: 40%, $space: hsl);\r\n  }\r\n\r\n  i {\r\n    @include flexbox(center, center);\r\n  }\r\n}\r\n\r\n.success_message {\r\n  margin: 1rem 0;\r\n  // padding: 0 0 1rem 0;\r\n  text-align: center;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn_container {\r\n  // background-color: rgb(132, 189, 189);\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: center;\r\n  // align-items: center;\r\n  column-gap: 1rem;\r\n}\r\n\r\n.password__container {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  // input {\r\n  //   width: 100%;\r\n  // }\r\n\r\n  span {\r\n    position: absolute;\r\n    right: 7px;\r\n    cursor: pointer;\r\n\r\n    i {\r\n      @include flexbox(center, center);\r\n      font-size: 18px;\r\n      color: $primary-dark-text-color;\r\n    }\r\n  }\r\n}\r\n\r\n// .loading_span {\r\n//   @include flexbox(center, center);\r\n//   gap: 0.5rem;\r\n// }\r\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "@use './reset';\n@use '../src/scss/common' as *;\n@use './../src/scss/variables' as *;\n\nhtml,\nbody {\n  // max-width: 100vw;\n  // background-color: red;\n}\n\nbody {\n  // background-color: $alice-blue;\n  background-color: white;\n}"], "names": [], "mappings": "AAEA;;;;AAOA;;;;;AAMA;;;;AAMA;;;;AAKA;;;;;;AAOA;;;;;AAKA;;;;;AAOA;;;;AAQA;EACE;;;;EAIA;;;;;;;;AAWF;;;;;;AAMA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;AASA;;;;AAtGA;;;;;;;;;;;;AAIA;;;;AAIA;;;;;;AAOA;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AASE;;;;;;;AAME;;;;;AAKA;;;;;;;;AAOE;;;;;AAMF;;;;;AAOJ;;;;;;;;;;;;;;AAQE;;;;AAMF;;;;;;;;;;;;AAME;;;;AAIA;;;;;;;AAKF;;;;;;;AAQA;;;;;;;AASA;;;;;;AASE;;;;;;AAKE;;;;;;;;;AAzIJ"}}]}