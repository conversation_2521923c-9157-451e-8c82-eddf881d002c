'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import styles from './AddressStage.module.scss'
import authStore from '@/src/stores/auth-store'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import cartStore from '@/src/stores/cart-store'
import {
  useCart,
  useDeleteCartItem,
  useUpdateCartCustomer,
} from '@/src/hooks/cart-hooks'
import { AddressFormInputs } from '@/src/components/account/addresses/ManageAddresses'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import Alert from '@/src/components/utils/alert/Alert'
import Logo from '@/src/components/utils/logo/Logo'
import Spinner from '@/src/components/utils/spinner/Spinner'
import Link from 'next/link'
import CartItemsList from '../../components/cart/CartItemsList'
import SelectedCartSummary from '../../components/cart/SelectedCartSummary'

const AddressChoice = () => {
  const router = useRouter()

  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const { cartId, setSelectedAddress, selectedAddress } = cartStore()
  const { isLoading, error, data } = useCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()
  const { updateCartCustomer, isPending: isUpdatingCart } =
    useUpdateCartCustomer()

  const [addressesReady, setAddressesReady] = useState(false)
  const [cartUpdated, setCartUpdated] = useState(false)

  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login')
    }
  }, [isLoggedIn, router])

  // Update cart with authenticated customer when page loads
  // Update cart with authenticated customer when page loads
  useEffect(() => {
    if (
      isLoggedIn &&
      cartId &&
      customer &&
      !cartUpdated &&
      !isUpdatingCart &&
      !data?.customer // <-- only if cart has no customer yet
    ) {
      updateCartCustomer({})
      setCartUpdated(true)
    }
  }, [
    isLoggedIn,
    cartId,
    customer,
    cartUpdated,
    isUpdatingCart,
    updateCartCustomer,
    data?.customer, // <-- add to dependency array
  ])

  useEffect(() => {
    if (customer?.address && customer.address.length > 0) {
      if (!selectedAddress || Object.keys(selectedAddress).length === 0) {
        setSelectedAddress(customer.address[0])
      }
      setAddressesReady(true)
    }
  }, [customer, selectedAddress, setSelectedAddress])

  // Handle out-of-stock items by removing them
  useEffect(() => {
    if (data && data?.cart_items?.length > 0) {
      const outOfStockItems = data.cart_items.filter(
        (item) => item.product_variant.stock_qty === 0
      )

      if (outOfStockItems.length > 0) {
        outOfStockItems.forEach((item) => {
          deleteCartItem(item.id) // Remove each out-of-stock item from the cart
        })
      }
    }
  }, [data, deleteCartItem])

  const handleAddressChange = (address: AddressFormInputs) => {
    setSelectedAddress(address)
  }

  return (
    <>
      {!cartId ? (
        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />
      ) : (
        <>
          {isLoading ? (
            <Spinner color='#0091CF' size={20} loading />
          ) : (
            <>
              {error ? (
                <Alert variant='error' message={error.message} />
              ) : (
                <>
                  {customer?.address?.length === 0 ||
                  customer?.phone_number === '' ? (
                    <>
                      <div className='logo_header'>
                        <Logo />
                      </div>
                      <div className={styles.missing_addresses}>
                        <Alert
                          variant='warning'
                          textSize='20'
                          message="
                        You haven't added a shipping address yet. 
                        Please add one along with a phone number to continue with checkout. Thank you! 😊"
                        />
                        <section className='btn_container'>
                          <button
                            className='empty_btn'
                            onClick={() => router.push('/account/profile')}
                          >
                            Update Profile
                          </button>
                        </section>
                      </div>
                    </>
                  ) : (
                    <>
                      {!data || data.cart_items.length === 0 ? (
                        <div className={styles.empty_cart}>
                          <p>
                            Your cart is empty. Add some products to the cart to
                            checkout!
                          </p>
                          <Link href='/'>Go Shopping </Link>
                        </div>
                      ) : (
                        <>
                          <section>
                            <section
                              className={`container ${styles.address_stage}`}
                            >
                              <h3>Address Choices</h3>
                              <div className={styles.contact_details}>
                                <h3>Contact Details: </h3>
                                <p>
                                  Deliver to: {customer?.first_name}{' '}
                                  {customer?.last_name}
                                </p>
                                <p>Phone: {customer?.phone_number}</p>
                                <p>Email to: {customer?.email}</p>
                              </div>
                              <hr />
                              <div className={styles.cart}>
                                <CartItemsList cartItems={data.cart_items} />
                                <SelectedCartSummary
                                  cartItems={data.cart_items}
                                  totalPrice={data?.total_price}
                                  item_count={data?.item_count}
                                  cart_weight={data?.cart_weight}
                                />
                              </div>
                              <hr />
                              {/* Render the addresses only when addresses are ready */}
                              {addressesReady && (
                                <div className={styles.address_selection}>
                                  <h3>Choose a shipping address: </h3>
                                  {customer?.address?.map((address) => (
                                    <address key={address.id}>
                                      <input
                                        type='radio'
                                        id={`address-${address.id}`}
                                        name='address'
                                        value={address.id}
                                        checked={
                                          selectedAddress?.id === address.id
                                        }
                                        onChange={() =>
                                          handleAddressChange(address)
                                        }
                                      />
                                      <label htmlFor={`address-${address.id}`}>
                                        {address.full_name},{' '}
                                        {address.street_name},{' '}
                                        {address.city_or_village}
                                      </label>
                                    </address>
                                  ))}
                                  <button
                                    onClick={() =>
                                      router.push('/checkout/payment-choice')
                                    }
                                  >
                                    Use this address
                                  </button>
                                </div>
                              )}
                              <hr />
                            </section>
                          </section>
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </>
      )}
    </>
  )
}

export default AddressChoice
