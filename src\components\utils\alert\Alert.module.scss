@use '../../../styles/variables.scss' as *;

.alert {
  padding: $spacing-3 $spacing-4;
  border-radius: $border-radius-md;
  margin: $spacing-3 0;

  p {
    margin: 0;
    font-size: $font-size-2;
  }
}

.info {
  background-color: $info-bg;
  color: $info-text;
}

.warning {
  background-color: $warning-bg;
  color: $warning-text;
}

.error {
  background-color: $error-bg;
  color: $error-text;
}

.success {
  background-color: $success-bg;
  color: $success-text;
}

.highlight {
  font-weight: bold;
  background-color: rgba(255, 255, 0, 0.3);
  padding: 0 2px;
  border-radius: 2px;
}
