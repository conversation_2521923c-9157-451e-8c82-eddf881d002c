{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/empty-cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/empty-cart/EmptyCart.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/empty-cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/src/components/utils/empty-cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\r\n@use '../../../scss/mixins' as *;\r\n@use 'sass:map';\r\n\r\n.empty_cart {\r\n  @include flexbox(center, center, column);\r\n  margin: 3rem auto 0 auto;\r\n  text-align: center;\r\n  max-width: 500px;\r\n  padding: $padding-5;\r\n\r\n  .icon_section {\r\n    margin-bottom: $padding-4;\r\n\r\n    svg {\r\n      font-size: 64px;\r\n      color: $primary-lighter-text-color;\r\n    }\r\n\r\n    @include mobile {\r\n      svg {\r\n        font-size: 48px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_section {\r\n    margin-bottom: $padding-5;\r\n\r\n    .heading {\r\n      font-size: $font-size-5;\r\n      color: $primary-dark-text-color;\r\n      font-weight: map.get($font-weight, 'bold');\r\n      margin-bottom: $padding-3;\r\n      line-height: 1.3;\r\n\r\n      @include mobile {\r\n        font-size: $font-size-4;\r\n      }\r\n    }\r\n\r\n    .description {\r\n      font-size: $font-size-3;\r\n      color: $primary-lighter-text-color;\r\n      line-height: 1.5;\r\n      margin: 0;\r\n\r\n      @include mobile {\r\n        font-size: $font-size-3;\r\n      }\r\n    }\r\n  }\r\n\r\n  .action_section {\r\n    .action_button {\r\n      @include btn(white, $primary-blue);\r\n      text-decoration: none;\r\n      padding: $padding-3 $padding-5;\r\n      border-radius: $border-radius-1;\r\n      font-size: $font-size-3;\r\n      font-weight: map.get($font-weight, 'medium');\r\n      transition: background-color 0.3s ease, transform 0.2s ease;\r\n      display: inline-block;\r\n\r\n      &:hover {\r\n        background-color: $lighten-blue;\r\n        transform: translateY(-1px);\r\n        text-decoration: none;\r\n      }\r\n\r\n      &:focus {\r\n        outline: 2px solid $primary-blue;\r\n        outline-offset: 2px;\r\n      }\r\n\r\n      &:active {\r\n        transform: translateY(0);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAIA;;;;;;;;;;;AAOE;;;;AAGE;;;;;AAsBF;EAhBI;;;;;AAMJ;;;;AAGE;;;;;;;;AAOF;EAPE;;;;;AAYA;;;;;;;AALF;EAKE;;;;;AAaA;;;;;;;;;;;;;;;AAUE;;;;;;AAMA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/underlay/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/underlay/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\r\n@use '../../../scss/mixins' as *;\r\n\r\n.underlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100vw;\r\n  /* Set the width to 100% of the viewport width */\r\n  height: 100vh;\r\n  /* Set the height to 100% of the viewport height */\r\n  z-index: 20;\r\n  /* Set a z-index value if needed */\r\n  @include flexbox(center, center);\r\n  /* Replaced direct flexbox properties with the flexbox mixin */\r\n}", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../../../src/scss/variables' as *;\n@use '../../../../../src/scss/mixins' as *;\n\n.cart_item {\n  // background-color: #e9e0e0;\n  margin: 0;\n  padding: 0.75rem;\n  display: grid;\n  grid-template-columns: 90px 1fr;\n  gap: 0.75rem;\n  box-shadow: $box-shadow-1;\n  width: 100%;\n  height: fit-content;\n}\n\n.cart_item__img {\n  // @include flexbox(flex-start, center);\n  // background-color: #e9b6b6;\n  img {\n    // @include flexbox(center, center);\n    width: 100%;\n    max-height: fit-content;\n    // height: auto;\n    object-fit: contain;\n  }\n}\n\n.cart_item__info {\n  @include flexbox(flex-start, flex-start, column);\n  gap: 0.2rem;\n\n  span {\n    // background-color: #e6d8d8;\n    font-size: $font-size-2;\n    color: $primary-lighter-text-color;\n    font-weight: bold;\n  }\n\n  .cart_item__title {\n    font-size: 16px;\n    font-weight: bold;\n\n    a {\n      color: $primary-dark;\n    }\n\n    &:hover {\n      text-decoration: underline;\n      color: $primary-blue;\n    }\n  }\n\n  .cart_item__extra_data {\n    @include flexbox(flex-start, center);\n    gap: 0.5rem;\n    font-size: 13.5px;\n    color: $primary-lighter-text-color;\n\n    p:first-child {\n      font-weight: bold;\n    }\n  }\n}\n\n.cart_item__quantity {\n  grid-column: 1 / 3;\n  @include flexbox(flex-start, center, column);\n  // background-color: #96eea2;\n  // width: 100%;\n  // margin: 0 auto;\n  // column-gap: 1rem;\n\n  div:first-child {\n    // background-color: #978c8c;\n    @include flexbox(flex-start, center);\n    column-gap: 0.8rem;\n\n    p {\n      font-weight: bold;\n      color: $primary-dark-text-color;\n    }\n\n    button {\n      padding: 4px;\n      border: 1.6px solid #fff;\n      border-radius: 2px;\n      transition: all 0.2s ease-out;\n      // background-color: $sky-lighter-blue;\n\n      i {\n        @include flexbox(center, center);\n      }\n\n      &:hover {\n        border: 1.6px solid $primary-blue;\n        color: $primary-blue;\n      }\n\n      &:disabled:hover {\n        border: 1.6px solid #fff;\n        color: inherit;\n        cursor: not-allowed;\n      }\n    }\n\n    button:nth-child(5) {\n      background-color: $error-red;\n      border: 1.6px solid $error-red;\n      transition: all 0.3s ease;\n\n      i {\n        font-weight: bold;\n        color: #fff;\n      }\n\n      &:hover {\n        background-color: darken($error-red, 10%);\n        // border: 1.6px solid $error-red;\n\n        i {\n          color: #fff;\n        }\n      }\n    }\n  }\n\n  p:last-child {\n    margin: 10px 0;\n    color: $primary-red;\n    font-weight: bold;\n    text-transform: none;\n    // background-color: #c2a2a2;\n  }\n}\n\n@media (width > $tablet) {\n  .cart_item {\n    grid-template-columns: 110px 1fr auto;\n  }\n\n  .cart_item__quantity {\n    width: max-content;\n    grid-column: 3 / 4;\n    justify-content: center;\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;;;;;AAeE;;;;;;;AASF;;;;;;;;AAIE;;;;;;AAOA;;;;;AAIE;;;;AAIA;;;;;AAMF;;;;;;;;;;AAME;;;;AAMJ;;;;;;;;AAQE;;;;;;;;AAKE;;;;;AAKA;;;;;;;AAOE;;;;;;;AAIA;;;;;AAKA;;;;;;AAOF;;;;;;AAKE;;;;;AAKA;;;;AAIE;;;;AAON;;;;;;;AASF;EACE;;;;EAIA", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/tooltip/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/tooltip/Tooltip.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/tooltip/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../scss/mixins' as *;\n\n// Base tooltip styles\n.tooltip {\n  // display: flex;\n  i {\n    @include flexbox(center, center);\n  }\n  // background-color: #333;\n  position: relative;\n  cursor: pointer;\n}\n\n// Tooltip content (the actual tooltip bubble)\n.tooltip::after {\n  content: attr(data-tooltip);\n  position: absolute;\n  background-color: #333;\n  color: #fff;\n  padding: 4px 8px;\n  border-radius: 4px;\n  opacity: 0;\n  visibility: hidden;\n  white-space: nowrap;\n  font-size: 12px;\n  pointer-events: none;\n  transition: opacity 0.2s ease, visibility 0.3s ease;\n  z-index: 1000;\n}\n\n// Position variants\n.tooltip--top::after {\n  bottom: calc(100% + 6px);\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.tooltip--bottom::after {\n  top: calc(100% + 6px);\n  left: 50%;\n  transform: translateX(-50%);\n}\n\n.tooltip--left::after {\n  right: calc(100% + 6px);\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.tooltip--right::after {\n  left: calc(100% + 6px);\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n// Show tooltip on hover (default behavior)\n.tooltip--hover:hover::after {\n  opacity: 1;\n  visibility: visible;\n}\n\n// Show tooltip based on condition (for disabled buttons, etc.)\n.tooltip--condition::after {\n  opacity: 1;\n  visibility: visible;\n}\n\n// Arrow indicators for better visual connection\n.tooltip::before {\n  content: '';\n  position: absolute;\n  border: 4px solid transparent;\n  opacity: 0;\n  visibility: hidden;\n  transition: opacity 0.2s ease, visibility 0.3s ease;\n  z-index: 1001;\n}\n\n// Arrow positions\n.tooltip--top::before {\n  bottom: calc(100% + 2px);\n  left: 50%;\n  transform: translateX(-50%);\n  border-top-color: #333;\n}\n\n.tooltip--bottom::before {\n  top: calc(100% + 2px);\n  left: 50%;\n  transform: translateX(-50%);\n  border-bottom-color: #333;\n}\n\n.tooltip--left::before {\n  right: calc(100% + 2px);\n  top: 50%;\n  transform: translateY(-50%);\n  border-left-color: #333;\n}\n\n.tooltip--right::before {\n  left: calc(100% + 2px);\n  top: 50%;\n  transform: translateY(-50%);\n  border-right-color: #333;\n}\n\n// Show arrows on hover\n.tooltip--hover:hover::before {\n  opacity: 1;\n  visibility: visible;\n}\n\n// Show arrows based on condition\n.tooltip--condition::before {\n  opacity: 1;\n  visibility: visible;\n}\n\n// Responsive adjustments\n@media (max-width: 768px) {\n  .tooltip::after {\n    font-size: 11px;\n    padding: 3px 6px;\n    max-width: 200px;\n    white-space: normal;\n    word-wrap: break-word;\n  }\n}\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;AAEE;;;;;;;AASF;;;;;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAOA;;;;;AAYA;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAQA;;;;;AAYA;EACE", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../../../src/scss/variables' as *;\n@use '../../../../../src/scss/mixins' as *;\n\n.summary_container {\n  background-color: $sky-lighter-blue;\n  border: 1px solid #d1d5db;\n  border-radius: $border-radius-2;\n  padding: $padding-4;\n  margin-top: $padding-3;\n  box-shadow: $box-shadow-1;\n}\n\n.summary_header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $padding-3;\n  padding-bottom: $padding-2;\n  border-bottom: 1px solid #d1d5db;\n\n  h3 {\n    margin: 0;\n    font-size: $font-size-5;\n    font-weight: map-get($font-weight, 'medium');\n    color: $primary-dark-text-color;\n  }\n\n  .item_count {\n    font-size: $font-size-2;\n    color: $primary-lighter-text-color;\n    background-color: #f9fafb;\n    padding: $padding-1 $padding-2;\n    border-radius: $border-radius-1;\n  }\n}\n\n.summary_details {\n  position: relative;\n}\n\n.summary_row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: $padding-2 0;\n  border-bottom: 1px solid rgba(#d1d5db, 0.5);\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  span {\n    font-size: $font-size-3;\n\n    &:first-child {\n      color: $primary-lighter-text-color;\n    }\n  }\n\n  .price {\n    font-weight: map-get($font-weight, 'medium');\n    color: $primary-blue;\n    font-size: $font-size-3;\n  }\n}\n\n// Price summary section styles (from PriceSummary)\n.price_summary_section {\n  margin-top: $padding-3;\n\n  .summary_row {\n    @include flexbox(space-between, center);\n    width: 100%;\n    font-size: 1.2rem;\n    padding: $padding-2 0;\n    border-bottom: 1px solid rgba(#d1d5db, 0.5);\n\n    &:last-child {\n      border-bottom: none;\n    }\n\n    span:first-child {\n      font-weight: map-get($font-weight, 'medium');\n      color: $primary-dark-text-color;\n\n      @include flexbox(flex-start, center);\n      column-gap: 2px;\n    }\n\n    span:last-child {\n      font-weight: map-get($font-weight, 'medium');\n      color: $primary-blue;\n    }\n  }\n\n  .total_row {\n    border-top: 1px solid $primary-blue;\n    padding-top: 1rem;\n    margin-top: 0.5rem;\n\n    span {\n      font-size: 1.3rem !important;\n    }\n  }\n}\n\n.divider {\n  height: 1px;\n  background-color: #d1d5db;\n  margin: $padding-3 0 $padding-2 0;\n}\n\n// Checkout button styles (from PriceSummary)\n.checkout_section {\n  margin-top: $padding-3;\n  @include flexbox(center, center);\n}\n\n.checkout_button {\n  @include btn(#fff, $lighten-blue);\n  width: 100%;\n  padding: 1rem 0;\n  font-weight: map-get($font-weight, 'medium');\n  text-transform: uppercase;\n  letter-spacing: 0.7px;\n  transition: all 0.3s ease;\n  border: none;\n  cursor: pointer;\n\n  &:hover {\n    background-color: darken($lighten-blue, 10%);\n    color: darken(#fff, 15%);\n  }\n}\n\n.comparison_section {\n  margin-top: $padding-3;\n\n  .comparison_header {\n    margin-bottom: $padding-2;\n\n    span {\n      font-size: $font-size-2;\n      font-weight: map-get($font-weight, 'medium');\n      color: $primary-lighter-text-color;\n      text-transform: uppercase;\n      letter-spacing: 0.5px;\n    }\n  }\n\n  .comparison_row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: $padding-1 0;\n    font-size: $font-size-2;\n\n    .savings {\n      color: $primary-green;\n      font-weight: map-get($font-weight, 'medium');\n    }\n  }\n}\n\n.no_selection {\n  text-align: center;\n  padding: $padding-5 $padding-4;\n  color: $primary-lighter-text-color;\n\n  p {\n    margin: 0 0 $padding-1 0;\n    font-size: $font-size-3;\n    font-weight: map-get($font-weight, 'medium');\n  }\n\n  span {\n    font-size: $font-size-2;\n    color: #9ca3af;\n  }\n}\n\n.loading_overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(white, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: $border-radius-2;\n\n  span {\n    font-size: $font-size-2;\n    color: $primary-lighter-text-color;\n    font-style: italic;\n  }\n}\n\n// Mobile responsiveness\n@media (max-width: $mobile) {\n  .summary_container {\n    padding: $padding-3;\n    margin-top: $padding-2;\n  }\n\n  .summary_header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: $padding-1;\n\n    h3 {\n      font-size: $font-size-3;\n    }\n\n    .item_count {\n      font-size: $font-size-1;\n    }\n  }\n\n  .summary_row {\n    padding: $padding-1 0;\n\n    span {\n      font-size: $font-size-2;\n    }\n  }\n\n  .comparison_row {\n    font-size: $font-size-1;\n  }\n\n  .price_summary_section {\n    .summary_row {\n      font-size: 1rem;\n      padding: $padding-1 0;\n\n      span {\n        font-size: $font-size-2;\n      }\n    }\n\n    .total_row {\n      span {\n        font-size: 1.1rem !important;\n      }\n    }\n  }\n\n  .checkout_button {\n    padding: $padding-3 0;\n    font-size: $font-size-2;\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;;AASA;;;;;;;;;AAQE;;;;;;;AAOA;;;;;;;;AASF;;;;AAIA;;;;;;;;AAOE;;;;AAIA;;;;AAGE;;;;AAKF;;;;;;AAQF;;;;AAGE;;;;;;;;;;;AAOE;;;;AAIA;;;;;;;;;;AAQA;;;;;AAMF;;;;;;AAKE;;;;AAMJ;;;;;;AAOA;;;;;;;;AAKA;;;;;;;;;;;;;;;;;;AAWE;;;;;AAMF;;;;AAGE;;;;AAGE;;;;;;;;AASF;;;;;;;;AAOE;;;;;AAOJ;;;;;;AAKE;;;;;;AAMA;;;;;AAMF;;;;;;;;;;;;;AAYE;;;;;;AAQF;EACE;;;;;EAKA;;;;;;EAKE;;;;EAIA;;;;EAKF;;;;EAGE;;;;EAKF;;;;EAKE;;;;;EAIE;;;;EAMA;;;;EAMJ", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/checkout/cart/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/checkout/cart/Cart.module.scss"], "sourcesContent": ["@use '../../../../../src/scss/variables' as *;\r\n@use '../../../../../src/scss/mixins' as *;\r\n\r\n.cart {\r\n  // background-color: red;\r\n  h2 {\r\n    margin: 1rem;\r\n    font-weight: bold;\r\n    font-size: 25px;\r\n  }\r\n\r\n  &__cart_items {\r\n    margin: 1rem 0 0 0;\r\n    display: grid;\r\n    grid-template-columns: 1fr auto;\r\n    gap: 1rem;\r\n\r\n    @media (max-width: $tablet) {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n\r\n  &__summaries {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    min-width: 300px;\r\n\r\n    @media (max-width: $tablet) {\r\n      min-width: 100%;\r\n    }\r\n  }\r\n\r\n  // .cart__cart_items {\r\n  //   display: grid;\r\n  //   grid-template-columns: 1fr;\r\n  // }\r\n}\r\n\r\n@media (min-width: $tablet) {\r\n  .cart__cart_items {\r\n    grid-template-columns: 2fr 1fr;\r\n    gap: 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAKE;;;;;;AAMA;;;;;;;AAME;EANF;;;;;AAWA;;;;;;;AAME;EANF;;;;;AAiBF;EACE", "debugId": null}}]}