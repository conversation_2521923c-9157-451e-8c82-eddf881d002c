{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"action_button\": \"EmptyCart-module-scss-module__p5kxma__action_button\",\n  \"action_section\": \"EmptyCart-module-scss-module__p5kxma__action_section\",\n  \"content_section\": \"EmptyCart-module-scss-module__p5kxma__content_section\",\n  \"description\": \"EmptyCart-module-scss-module__p5kxma__description\",\n  \"empty_cart\": \"EmptyCart-module-scss-module__p5kxma__empty_cart\",\n  \"heading\": \"EmptyCart-module-scss-module__p5kxma__heading\",\n  \"icon_section\": \"EmptyCart-module-scss-module__p5kxma__icon_section\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/empty-cart/EmptyCart.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport { FaShoppingCart } from 'react-icons/fa'\r\nimport styles from './EmptyCart.module.scss'\r\n\r\ninterface Props {\r\n  message?: string\r\n  showIcon?: boolean\r\n  actionText?: string\r\n  actionHref?: string\r\n}\r\n\r\nconst EmptyCart = ({\r\n  message,\r\n  showIcon = true,\r\n  actionText = \"Go Shopping\",\r\n  actionHref = \"/\"\r\n}: Props) => {\r\n  return (\r\n    <div className={styles.empty_cart} role=\"region\" aria-label=\"Empty cart\">\r\n      {showIcon && (\r\n        <div className={styles.icon_section} aria-hidden=\"true\">\r\n          <FaShoppingCart />\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.content_section}>\r\n        <h2 className={styles.heading}>\r\n          {message ? message : \"Your cart is empty\"}\r\n        </h2>\r\n        <p className={styles.description}>\r\n          Add some products to your cart to get started with your shopping journey.\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.action_section}>\r\n        <Link\r\n          href={actionHref}\r\n          className={styles.action_button}\r\n          aria-label={`${actionText} - Browse products to add to your cart`}\r\n        >\r\n          {actionText}\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\nexport default EmptyCart"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AASA,MAAM,YAAY;QAAC,EACjB,OAAO,EACP,WAAW,IAAI,EACf,aAAa,aAAa,EAC1B,aAAa,GAAG,EACV;IACN,qBACE,6LAAC;QAAI,WAAW,mLAAM,CAAC,UAAU;QAAE,MAAK;QAAS,cAAW;;YACzD,0BACC,6LAAC;gBAAI,WAAW,mLAAM,CAAC,YAAY;gBAAE,eAAY;0BAC/C,cAAA,6LAAC,mKAAc;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAW,mLAAM,CAAC,eAAe;;kCACpC,6LAAC;wBAAG,WAAW,mLAAM,CAAC,OAAO;kCAC1B,UAAU,UAAU;;;;;;kCAEvB,6LAAC;wBAAE,WAAW,mLAAM,CAAC,WAAW;kCAAE;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAW,mLAAM,CAAC,cAAc;0BACnC,cAAA,6LAAC,0KAAI;oBACH,MAAM;oBACN,WAAW,mLAAM,CAAC,aAAa;oBAC/B,cAAY,AAAC,GAAa,OAAX,YAAW;8BAEzB;;;;;;;;;;;;;;;;;AAKX;KAlCM;uCAmCS", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/stores/order-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface OrderStoreShape {\r\n  orderId: number | null\r\n  setOrderId: (id: number | null) => void\r\n  // Define other state properties and actions if you uncomment the code for order and customer\r\n  // order: Order;\r\n  // setOrder: (newOrder: Partial<Order>) => void;\r\n  // clearOrder: () => void;\r\n  // customer: Customer;\r\n}\r\n\r\nconst orderStore = create<OrderStoreShape>()(\r\n  persist(\r\n    (set) => ({\r\n      // order: {\r\n      //   orderedProducts: [],\r\n      //   subtotal: 0\r\n      // },\r\n      // setOrder: (newOrder) => set((state) => ({ order: { ...state.order, ...newOrder } })),\r\n      // clearOrder: () => set({ order: { orderedProducts: [], subtotal: 0 } }),\r\n      // customer: {},\r\n      orderId: null,\r\n      setOrderId: (id) => set({ orderId: id }),\r\n    }),\r\n    {\r\n      name: 'order_store',\r\n      // storage: createJSONStorage(() => sessionStorage) // Specify the storage engine (localStorage or sessionStorage)\r\n    }\r\n  )\r\n)\r\n\r\nexport default orderStore\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAYA,MAAM,aAAa,IAAA,qJAAM,IACvB,IAAA,2JAAO,EACL,CAAC,MAAQ,CAAC;QACR,WAAW;QACX,yBAAyB;QACzB,gBAAgB;QAChB,KAAK;QACL,wFAAwF;QACxF,0EAA0E;QAC1E,gBAAgB;QAChB,SAAS;QACT,YAAY,CAAC,KAAO,IAAI;gBAAE,SAAS;YAAG;IACxC,CAAC,GACD;IACE,MAAM;AAER;uCAIW", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/order-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport APIClient from '../lib/api-client'\r\nimport { createOrderShape, OrderShape } from '../types/order-types'\r\nimport { CACHE_KEY_ORDER_ITEMS, CACHE_KEY_ORDERS } from '../constants/constants'\r\nimport orderStore from '../stores/order-store'\r\nimport cartStore from '../stores/cart-store'\r\nimport { useState } from 'react'\r\n\r\n\r\nexport const useOrder = (orderId: number) => {\r\n\r\n  const apiClient = new APIClient<OrderShape>(`/orders/${orderId}`)\r\n\r\n  return useQuery({\r\n    // queryKey: [CACHE_KEY_ORDER_ITEMS, orderId],\r\n    queryKey: [CACHE_KEY_ORDER_ITEMS],\r\n    queryFn: () => apiClient.get(),\r\n    staleTime: 0,\r\n  })\r\n\r\n}\r\n\r\nexport const useGetAllOrders = (page: number, queryString: string = '') => {\r\n  const apiClient = new APIClient<OrderShape>(`/orders/`)\r\n  const queryParams = new URLSearchParams(queryString)\r\n\r\n  queryParams.set('page', page.toString())\r\n\r\n  return useQuery({\r\n    queryKey: [CACHE_KEY_ORDERS, page, queryString],\r\n    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),\r\n    // staleTime: 1000 * 60 * 5, // 5 minutes\r\n  })\r\n}\r\n\r\nexport const useCreateOrder = () => {\r\n  // const navigate = useNavigate()\r\n  const { setOrderId } = orderStore()\r\n  const { setCartId } = cartStore()\r\n\r\n  const apiClient = new APIClient<OrderShape, createOrderShape>(`/orders/`)\r\n\r\n  const createOrder = useMutation<OrderShape, Error, createOrderShape>({\r\n    mutationFn: (data) => apiClient.post(data),\r\n    onSuccess: (data) => {\r\n      console.log(data)\r\n      setOrderId(data.id)\r\n      setCartId(null)\r\n      localStorage.removeItem('cart_store')\r\n      if (data.id) {\r\n        // navigate(`/checkout/order/${data.id}/`)\r\n      }\r\n    },\r\n  })\r\n\r\n  return { createOrder }\r\n}\r\n\r\nexport const useDeleteOrder = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false)\r\n  const [orderToDelete, setOrderToDelete] = useState<number | null>(null)\r\n  const queryClient = useQueryClient()\r\n  const apiClient = new APIClient(`/orders`)\r\n\r\n  const deleteOrder = useMutation({\r\n    mutationFn: (orderId: number) => apiClient.delete(orderId),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [CACHE_KEY_ORDERS]\r\n      })\r\n    },\r\n  })\r\n\r\n  const handleDeleteClick = (orderId: number) => {\r\n    setOrderToDelete(orderId)\r\n    setIsModalOpen(true)\r\n  }\r\n\r\n  const confirmDelete = () => {\r\n    if (orderToDelete !== null) {\r\n      deleteOrder.mutate(orderToDelete)\r\n      setIsModalOpen(false)\r\n      setOrderToDelete(null)\r\n    }\r\n  }\r\n\r\n  const cancelDelete = () => {\r\n    setIsModalOpen(false)\r\n    setOrderToDelete(null)\r\n  }\r\n\r\n  return {\r\n    deleteOrder,\r\n    isModalOpen,\r\n    handleDeleteClick,\r\n    confirmDelete,\r\n    cancelDelete\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;AAGO,MAAM,WAAW,CAAC;;IAEvB,MAAM,YAAY,IAAI,yIAAS,CAAa,AAAC,WAAkB,OAAR;IAEvD,OAAO,IAAA,0LAAQ,EAAC;QACd,8CAA8C;QAC9C,UAAU;YAAC,yJAAqB;SAAC;QACjC,OAAO;iCAAE,IAAM,UAAU,GAAG;;QAC5B,WAAW;IACb;AAEF;GAXa;;QAIJ,0LAAQ;;;AASV,MAAM,kBAAkB,SAAC;QAAc,+EAAsB;;IAClE,MAAM,YAAY,IAAI,yIAAS,CAAc;IAC7C,MAAM,cAAc,IAAI,gBAAgB;IAExC,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;IAErC,OAAO,IAAA,0LAAQ,EAAC;QACd,UAAU;YAAC,oJAAgB;YAAE;YAAM;SAAY;QAC/C,OAAO;wCAAE,IAAM,UAAU,MAAM,CAAC;oBAAE,QAAQ,OAAO,WAAW,CAAC;gBAAa;;IAE5E;AACF;IAXa;;QAMJ,0LAAQ;;;AAOV,MAAM,iBAAiB;;IAC5B,iCAAiC;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,6IAAU;IACjC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,4IAAS;IAE/B,MAAM,YAAY,IAAI,yIAAS,CAAgC;IAE/D,MAAM,cAAc,IAAA,gMAAW,EAAsC;QACnE,UAAU;uDAAE,CAAC,OAAS,UAAU,IAAI,CAAC;;QACrC,SAAS;uDAAE,CAAC;gBACV,QAAQ,GAAG,CAAC;gBACZ,WAAW,KAAK,EAAE;gBAClB,UAAU;gBACV,aAAa,UAAU,CAAC;gBACxB,IAAI,KAAK,EAAE,EAAE;gBACX,0CAA0C;gBAC5C;YACF;;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;IArBa;;QAOS,gMAAW;;;AAgB1B,MAAM,iBAAiB;;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,yKAAQ,EAAgB;IAClE,MAAM,cAAc,IAAA,2MAAc;IAClC,MAAM,YAAY,IAAI,yIAAS,CAAE;IAEjC,MAAM,cAAc,IAAA,gMAAW,EAAC;QAC9B,UAAU;uDAAE,CAAC,UAAoB,UAAU,MAAM,CAAC;;QAClD,SAAS;uDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC,oJAAgB;qBAAC;gBAC9B;YACF;;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,gBAAgB;QACpB,IAAI,kBAAkB,MAAM;YAC1B,YAAY,MAAM,CAAC;YACnB,eAAe;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,iBAAiB;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IAxCa;;QAGS,2MAAc;QAGd,gMAAW", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/checkout-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery } from \"@tanstack/react-query\"\r\nimport APIClient from \"../lib/api-client\"\r\nimport { useOrder } from \"./order-hooks\"\r\nimport { PaymentOptionsShape } from \"../types/types\"\r\n\r\n\r\ninterface ClientSecretShape {\r\n  client_secret: string\r\n}\r\n\r\ninterface CaptureOrderRequest {\r\n  paypal_order_id: string\r\n}\r\n\r\ninterface CaptureOrderResponse {\r\n  status: string\r\n  payment_status: string\r\n}\r\n\r\ninterface PayPalOrderRequest {\r\n  order_id: number\r\n  amount: string\r\n}\r\n\r\ninterface PayPalOrderResponse {\r\n  id: string\r\n  status: string\r\n}\r\n\r\n\r\nexport const useClientSecret = (orderId: number) => {\r\n  const { data: order } = useOrder(orderId)\r\n  const apiClient = new APIClient<ClientSecretShape>(`payments/payment-intent-secret/`)\r\n\r\n  return useQuery({\r\n    queryKey: ['stripeClientSecret', orderId],\r\n    queryFn: () => apiClient.get({\r\n      params: {\r\n        order_id: orderId\r\n      }\r\n    }),\r\n    // If order is not null or undefined and payment method is Stripe, then enable the query\r\n    enabled: !!order && order.payment_method.slug === 'stripe'\r\n  })\r\n}\r\n\r\nexport const usePaymentOptions = () => {\r\n  const apiClient = new APIClient<PaymentOptionsShape[]>('/payments/payment-options/')\r\n\r\n  const payOptions = useQuery({\r\n    queryKey: ['payment_options'],\r\n    queryFn: apiClient.get,\r\n    // keepPreviousData: true,\r\n    // refetchOnWindowFocus: false,\r\n    staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n    // initialData:  Here we can add categories as static data\r\n  })\r\n\r\n  return payOptions\r\n}\r\n\r\n// PayPal Hooks\r\nexport const useCaptureOrder = () => {\r\n  const apiClient = new APIClient<CaptureOrderResponse, CaptureOrderRequest>('/payments/capture-paypal-order/')\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CaptureOrderRequest) => apiClient.post(data)\r\n  })\r\n}\r\n\r\nexport const useCreatePayPalOrder = ({ orderId, amount }: { orderId: number, amount: number }) => {\r\n  const apiClient = new APIClient<PayPalOrderResponse, PayPalOrderRequest>('payments/create-paypal-order/')\r\n\r\n  return useMutation({\r\n    mutationFn: () => apiClient.post({\r\n      order_id: orderId,\r\n      amount: amount.toString()\r\n    })\r\n  })\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AACA;AACA;;;;;AA4BO,MAAM,kBAAkB,CAAC;;IAC9B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,IAAA,6IAAQ,EAAC;IACjC,MAAM,YAAY,IAAI,yIAAS,CAAqB;IAEpD,OAAO,IAAA,0LAAQ,EAAC;QACd,UAAU;YAAC;YAAsB;SAAQ;QACzC,OAAO;wCAAE,IAAM,UAAU,GAAG,CAAC;oBAC3B,QAAQ;wBACN,UAAU;oBACZ;gBACF;;QACA,wFAAwF;QACxF,SAAS,CAAC,CAAC,SAAS,MAAM,cAAc,CAAC,IAAI,KAAK;IACpD;AACF;GAda;;QACa,6IAAQ;QAGzB,0LAAQ;;;AAYV,MAAM,oBAAoB;;IAC/B,MAAM,YAAY,IAAI,yIAAS,CAAwB;IAEvD,MAAM,aAAa,IAAA,0LAAQ,EAAC;QAC1B,UAAU;YAAC;SAAkB;QAC7B,SAAS,UAAU,GAAG;QACtB,0BAA0B;QAC1B,+BAA+B;QAC/B,WAAW,KAAK,KAAK,KAAK;IAE5B;IAEA,OAAO;AACT;IAba;;QAGQ,0LAAQ;;;AAatB,MAAM,kBAAkB;;IAC7B,MAAM,YAAY,IAAI,yIAAS,CAA4C;IAE3E,OAAO,IAAA,gMAAW,EAAC;QACjB,UAAU;2CAAE,CAAC,OAA8B,UAAU,IAAI,CAAC;;IAC5D;AACF;IANa;;QAGJ,gMAAW;;;AAKb,MAAM,uBAAuB;QAAC,EAAE,OAAO,EAAE,MAAM,EAAuC;;IAC3F,MAAM,YAAY,IAAI,yIAAS,CAA0C;IAEzE,OAAO,IAAA,gMAAW,EAAC;QACjB,UAAU;gDAAE,IAAM,UAAU,IAAI,CAAC;oBAC/B,UAAU;oBACV,QAAQ,OAAO,QAAQ;gBACzB;;IACF;AACF;IATa;;QAGJ,gMAAW", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/checkout/payment-choice/PaymentChoice.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart\": \"PaymentChoice-module-scss-module__SCceZW__cart\",\n  \"cart_items_quantity\": \"PaymentChoice-module-scss-module__SCceZW__cart_items_quantity\",\n  \"contact_details\": \"PaymentChoice-module-scss-module__SCceZW__contact_details\",\n  \"contact_info\": \"PaymentChoice-module-scss-module__SCceZW__contact_info\",\n  \"logo\": \"PaymentChoice-module-scss-module__SCceZW__logo\",\n  \"payment_options\": \"PaymentChoice-module-scss-module__SCceZW__payment_options\",\n  \"payment_options_stage\": \"PaymentChoice-module-scss-module__SCceZW__payment_options_stage\",\n  \"place_order\": \"PaymentChoice-module-scss-module__SCceZW__place_order\",\n  \"price_summary\": \"PaymentChoice-module-scss-module__SCceZW__price_summary\",\n  \"prices\": \"PaymentChoice-module-scss-module__SCceZW__prices\",\n  \"retry_shipping_calc\": \"PaymentChoice-module-scss-module__SCceZW__retry_shipping_calc\",\n  \"shipping_address\": \"PaymentChoice-module-scss-module__SCceZW__shipping_address\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/underlay/Underlay.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"underlay\": \"Underlay-module-scss-module__PkWa8a__underlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.tsx"], "sourcesContent": ["import React from 'react'\r\nimport styles from './Underlay.module.scss'\r\n\r\ninterface Props {\r\n  children: React.ReactNode\r\n  isOpen: boolean\r\n  onClose?: () => void\r\n  bgOpacity?: number\r\n  // zIndex?: number\r\n}\r\n\r\nconst Underlay = ({ children, isOpen, onClose, bgOpacity = 0.3 }: Props) => {\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    if (e.target === e.currentTarget && onClose) {\r\n      onClose()\r\n    }\r\n  }\r\n\r\n  return isOpen ? (\r\n    <div\r\n      className={styles.underlay}\r\n      style={{ backgroundColor: `rgba(0, 0, 0, ${bgOpacity})` }}\r\n      onClick={handleOverlayClick}\r\n    >\r\n      {children}\r\n    </div>\r\n  ) : null\r\n}\r\n\r\nexport default Underlay"], "names": [], "mappings": ";;;;;AACA;;;AAUA,MAAM,WAAW;QAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,GAAG,EAAS;IAErE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,SAAS;YAC3C;QACF;IACF;IAEA,OAAO,uBACL,6LAAC;QACC,WAAW,6KAAM,CAAC,QAAQ;QAC1B,OAAO;YAAE,iBAAiB,AAAC,iBAA0B,OAAV,WAAU;QAAG;QACxD,SAAS;kBAER;;;;;mDAED;AACN;KAjBM;uCAmBS", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/spinner/Spinner.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport SyncLoader from 'react-spinners/SyncLoader'\r\nimport <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader'\r\nimport PulseLoader from 'react-spinners/PulseLoader'\r\nimport RiseLoader from 'react-spinners/RiseLoader'\r\nimport RotateLoader from 'react-spinners/RotateLoader'\r\nimport ScaleLoader from 'react-spinners/ScaleLoader'\r\nimport CircleLoader from 'react-spinners/CircleLoader'\r\nimport Underlay from '../underlay/Underlay'\r\n\r\ninterface Props {\r\n  loading: boolean\r\n  color?: string\r\n  size?: number\r\n  thickness?: number\r\n  bgOpacity?: number\r\n  useUnderlay?: boolean\r\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\r\n}\r\n\r\nconst Spinner = ({\r\n  loading,\r\n  color,\r\n  size = 150,\r\n  thickness = 5,\r\n  bgOpacity,\r\n  useUnderlay = false,\r\n  spinnerType = 'sync'\r\n}: Props) => {\r\n  const renderSpinner = () => {\r\n    const commonProps = {\r\n      color,\r\n      loading,\r\n      size,\r\n      thickness,\r\n      'aria-label': 'Loading Spinner',\r\n    }\r\n\r\n    switch (spinnerType) {\r\n      case 'clip':\r\n        return <ClipLoader {...commonProps} />\r\n      case 'pulse':\r\n        return <PulseLoader {...commonProps} />\r\n      case 'rise':\r\n        return <RiseLoader {...commonProps} />\r\n      case 'rotate':\r\n        return <RotateLoader {...commonProps} />\r\n      case 'scale':\r\n        return <ScaleLoader {...commonProps} />\r\n      case 'circle':\r\n        return <CircleLoader {...commonProps} />\r\n      case 'sync':\r\n      default:\r\n        return <SyncLoader {...commonProps} />\r\n    }\r\n  }\r\n\r\n  if (!useUnderlay) {\r\n    return loading ? renderSpinner() : null\r\n  }\r\n\r\n  return (\r\n    <Underlay isOpen={loading} bgOpacity={bgOpacity}>\r\n      {renderSpinner()}\r\n    </Underlay>\r\n  )\r\n}\r\n\r\nexport default Spinner"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,UAAU;QAAC,EACf,OAAO,EACP,KAAK,EACL,OAAO,GAAG,EACV,YAAY,CAAC,EACb,SAAS,EACT,cAAc,KAAK,EACnB,cAAc,MAAM,EACd;IACN,MAAM,gBAAgB;QACpB,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA,cAAc;QAChB;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,8JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,6LAAC,6JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,+JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;gBACH,qBAAO,6LAAC,8JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,6LAAC,+JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;YACL;gBACE,qBAAO,6LAAC,6JAAU;oBAAE,GAAG,WAAW;;;;;;QACtC;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,6LAAC,iKAAQ;QAAC,QAAQ;QAAS,WAAW;kBACnC;;;;;;AAGP;KA9CM;uCAgDS", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/button-state/ButtonState.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"loadingSpan\": \"ButtonState-module-scss-module__25_Fyq__loadingSpan\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/button-state/ButtonState.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport styles from './ButtonState.module.scss'\n\ninterface ButtonStateProps {\n  isLoading: boolean\n  loadingText?: string\n  buttonText: string\n  spinnerSize?: number\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\n  spinnerColor?: string\n  spinnerThickness?: number\n  children?: React.ReactNode\n}\n\nconst ButtonState: React.FC<ButtonStateProps> = ({\n  isLoading,\n  loadingText = 'Loading...',\n  buttonText,\n  spinnerSize = 20,\n  spinnerColor = '#ffffff',\n  spinnerType,\n  spinnerThickness = 2,\n}) => {\n  return (\n    <>\n      {isLoading ? (\n        <span className={styles.loadingSpan}>\n          <Spinner\n            loading={isLoading}\n            size={spinnerSize}\n            color={spinnerColor}\n            spinnerType={spinnerType}\n            thickness={spinnerThickness}\n          />\n          <span>{loadingText}</span>\n        </span>\n      ) : (\n        buttonText\n      )}\n    </>\n  )\n}\n\nexport default ButtonState"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,cAA0C;QAAC,EAC/C,SAAS,EACT,cAAc,YAAY,EAC1B,UAAU,EACV,cAAc,EAAE,EAChB,eAAe,SAAS,EACxB,WAAW,EACX,mBAAmB,CAAC,EACrB;IACC,qBACE;kBACG,0BACC,6LAAC;YAAK,WAAW,uLAAM,CAAC,WAAW;;8BACjC,6LAAC,+JAAO;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW;;;;;;8BAEb,6LAAC;8BAAM;;;;;;;;;;;uDAGT;;AAIR;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/tooltip/Tooltip.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"tooltip\": \"Tooltip-module-scss-module__vdbe-W__tooltip\",\n  \"tooltip--bottom\": \"Tooltip-module-scss-module__vdbe-W__tooltip--bottom\",\n  \"tooltip--condition\": \"Tooltip-module-scss-module__vdbe-W__tooltip--condition\",\n  \"tooltip--hover\": \"Tooltip-module-scss-module__vdbe-W__tooltip--hover\",\n  \"tooltip--left\": \"Tooltip-module-scss-module__vdbe-W__tooltip--left\",\n  \"tooltip--right\": \"Tooltip-module-scss-module__vdbe-W__tooltip--right\",\n  \"tooltip--top\": \"Tooltip-module-scss-module__vdbe-W__tooltip--top\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/tooltip/Tooltip.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react'\nimport styles from './Tooltip.module.scss'\n\ninterface TooltipProps {\n  children: ReactNode\n  content: string\n  position?: 'top' | 'bottom' | 'left' | 'right'\n  disabled?: boolean\n  className?: string\n  showOnHover?: boolean\n  showOnCondition?: boolean\n}\n\nconst Tooltip: React.FC<TooltipProps> = ({\n  children,\n  content,\n  position = 'top',\n  disabled = false,\n  className = '',\n  showOnHover = true,\n  showOnCondition = false\n}) => {\n  // Don't show tooltip if disabled or no content\n  if (disabled || !content) {\n    return <>{children}</>\n  }\n\n  const tooltipClasses = [\n    styles.tooltip,\n    styles[`tooltip--${position}`],\n    showOnHover ? styles['tooltip--hover'] : '',\n    showOnCondition ? styles['tooltip--condition'] : '',\n    className\n  ].filter(Boolean).join(' ')\n\n  return (\n    <span className={tooltipClasses} data-tooltip={content}>\n      {children}\n    </span>\n  )\n}\n\nexport default Tooltip\n"], "names": [], "mappings": ";;;;;AACA;;;AAYA,MAAM,UAAkC;QAAC,EACvC,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,kBAAkB,KAAK,EACxB;IACC,+CAA+C;IAC/C,IAAI,YAAY,CAAC,SAAS;QACxB,qBAAO;sBAAG;;IACZ;IAEA,MAAM,iBAAiB;QACrB,2KAAM,CAAC,OAAO;QACd,2KAAM,CAAC,AAAC,YAAoB,OAAT,UAAW;QAC9B,cAAc,2KAAM,CAAC,iBAAiB,GAAG;QACzC,kBAAkB,2KAAM,CAAC,qBAAqB,GAAG;QACjD;KACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,6LAAC;QAAK,WAAW;QAAgB,gBAAc;kBAC5C;;;;;;AAGP;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkout_button\": \"SelectedCartSummary-module-scss-module__HNBufq__checkout_button\",\n  \"checkout_section\": \"SelectedCartSummary-module-scss-module__HNBufq__checkout_section\",\n  \"comparison_header\": \"SelectedCartSummary-module-scss-module__HNBufq__comparison_header\",\n  \"comparison_row\": \"SelectedCartSummary-module-scss-module__HNBufq__comparison_row\",\n  \"comparison_section\": \"SelectedCartSummary-module-scss-module__HNBufq__comparison_section\",\n  \"divider\": \"SelectedCartSummary-module-scss-module__HNBufq__divider\",\n  \"item_count\": \"SelectedCartSummary-module-scss-module__HNBufq__item_count\",\n  \"loading_overlay\": \"SelectedCartSummary-module-scss-module__HNBufq__loading_overlay\",\n  \"no_selection\": \"SelectedCartSummary-module-scss-module__HNBufq__no_selection\",\n  \"price\": \"SelectedCartSummary-module-scss-module__HNBufq__price\",\n  \"price_summary_section\": \"SelectedCartSummary-module-scss-module__HNBufq__price_summary_section\",\n  \"savings\": \"SelectedCartSummary-module-scss-module__HNBufq__savings\",\n  \"summary_container\": \"SelectedCartSummary-module-scss-module__HNBufq__summary_container\",\n  \"summary_details\": \"SelectedCartSummary-module-scss-module__HNBufq__summary_details\",\n  \"summary_header\": \"SelectedCartSummary-module-scss-module__HNBufq__summary_header\",\n  \"summary_row\": \"SelectedCartSummary-module-scss-module__HNBufq__summary_row\",\n  \"total_row\": \"SelectedCartSummary-module-scss-module__HNBufq__total_row\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/cart/SelectedCartSummary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport cartStore from '@/src/stores/cart-store'\nimport { CartItemShape } from '@/src/types/store-types'\nimport { RiQuestionFill } from 'react-icons/ri'\nimport Tooltip from '@/src/components/utils/tooltip/Tooltip'\nimport styles from './SelectedCartSummary.module.scss'\n\ninterface SelectedCartSummaryProps {\n  cartItems: CartItemShape[]\n  showComparison?: boolean\n  selectedIds?: Set<number>\n  // New props from PriceSummary\n  totalPrice?: number\n  shippingCost?: number\n  packingCost?: number\n  grandTotal?: number\n  item_count?: number\n  cart_weight?: number\n  onCheckout?: () => void\n  isShippingCalculated?: boolean\n}\n\nconst SelectedCartSummary = ({\n  cartItems,\n  showComparison = false,\n  selectedIds,\n  // New props from PriceSummary\n  totalPrice,\n  shippingCost,\n  packingCost,\n  grandTotal,\n  item_count,\n  cart_weight,\n  onCheckout,\n  isShippingCalculated = false,\n}: SelectedCartSummaryProps) => {\n  const selectedItemIds = cartStore((s) => s.selectedItemIds)\n\n  // Prefer server-provided selection when available, otherwise fall back to persisted array\n  const safeSelectedCartItems = React.useMemo(() => {\n    if (selectedIds && selectedIds instanceof Set)\n      return new Set<number>(selectedIds)\n    return new Set<number>(\n      Array.isArray(selectedItemIds) ? selectedItemIds : []\n    )\n  }, [selectedIds, selectedItemIds])\n\n  // Calculate frontend totals for selected items\n  const selectedItems = cartItems.filter((item) =>\n    safeSelectedCartItems.has(item.id)\n  )\n  const selectedCount = selectedItems.length\n  const totalCount = cartItems.length\n  const selectedSubtotal = selectedItems.reduce(\n    (sum, item) => sum + item.qty_price,\n    0\n  )\n\n  if (selectedCount === 0) {\n    return (\n      <div className={styles.summary_container}>\n        <div className={styles.no_selection}>\n          <p>No items selected</p>\n          <span>Select items to see checkout summary</span>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={styles.summary_container}>\n      <div className={styles.summary_header}>\n        <h3>Selected Items Summary</h3>\n        <span className={styles.item_count}>\n          {selectedCount} of {totalCount} items selected\n        </span>\n      </div>\n\n      <div className={styles.summary_details}>\n        {/* PriceSummary functionality - show when props are provided */}\n        {(item_count !== undefined ||\n          cart_weight !== undefined ||\n          totalPrice !== undefined) && (\n          <>\n            <div className={styles.price_summary_section}>\n              {cart_weight !== undefined && (\n                <div className={styles.summary_row}>\n                  <span>\n                    Cart weight:\n                    <Tooltip\n                      content={`Shipping cost will be calculated after finalizing adding items to the cart.`}\n                      position='top'\n                    >\n                      <i>\n                        <RiQuestionFill />\n                      </i>\n                    </Tooltip>\n                  </span>\n                  <span className={styles.price}>{cart_weight}g</span>\n                </div>\n              )}\n              <div className={styles.summary_row}>\n                <span>Selected Subtotal:</span>\n                <span className={styles.price}>\n                  ${selectedSubtotal.toFixed(2)}\n                </span>\n              </div>\n\n              {isShippingCalculated && shippingCost !== undefined && (\n                <div className={styles.summary_row}>\n                  <span>Shipping cost:</span>\n                  <span className={styles.price}>\n                    ${shippingCost.toFixed(2)}\n                  </span>\n                </div>\n              )}\n\n              {isShippingCalculated &&\n                packingCost !== undefined &&\n                packingCost > 0 && (\n                  <div className={styles.summary_row}>\n                    <span>Packing cost:</span>\n                    <span className={styles.price}>\n                      ${packingCost.toFixed(2)}\n                    </span>\n                  </div>\n                )}\n\n              {isShippingCalculated && grandTotal !== undefined && (\n                <div className={`${styles.summary_row} ${styles.total_row}`}>\n                  <span>\n                    <strong>Total:</strong>\n                  </span>\n                  <span className={styles.price}>\n                    <strong>${grandTotal.toFixed(2)}</strong>\n                  </span>\n                </div>\n              )}\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Checkout button */}\n      {onCheckout && (\n        <div className={styles.checkout_section}>\n          <button className={styles.checkout_button} onClick={onCheckout}>\n            Proceed to checkout\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SelectedCartSummary\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AACA;;;AAPA;;;;;;AAwBA,MAAM,sBAAsB;QAAC,EAC3B,SAAS,EACT,iBAAiB,KAAK,EACtB,WAAW,EACX,8BAA8B;IAC9B,UAAU,EACV,YAAY,EACZ,WAAW,EACX,UAAU,EACV,UAAU,EACV,WAAW,EACX,UAAU,EACV,uBAAuB,KAAK,EACH;;IACzB,MAAM,kBAAkB,IAAA,4IAAS,EAAC,CAAC,IAAM,EAAE,eAAe;IAE1D,0FAA0F;IAC1F,MAAM,wBAAwB,wKAAK,CAAC,OAAO;8DAAC;YAC1C,IAAI,eAAe,uBAAuB,KACxC,OAAO,IAAI,IAAY;YACzB,OAAO,IAAI,IACT,MAAM,OAAO,CAAC,mBAAmB,kBAAkB,EAAE;QAEzD;6DAAG;QAAC;QAAa;KAAgB;IAEjC,+CAA+C;IAC/C,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAC,OACtC,sBAAsB,GAAG,CAAC,KAAK,EAAE;IAEnC,MAAM,gBAAgB,cAAc,MAAM;IAC1C,MAAM,aAAa,UAAU,MAAM;IACnC,MAAM,mBAAmB,cAAc,MAAM,CAC3C,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EACnC;IAGF,IAAI,kBAAkB,GAAG;QACvB,qBACE,6LAAC;YAAI,WAAW,kNAAM,CAAC,iBAAiB;sBACtC,cAAA,6LAAC;gBAAI,WAAW,kNAAM,CAAC,YAAY;;kCACjC,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,qBACE,6LAAC;QAAI,WAAW,kNAAM,CAAC,iBAAiB;;0BACtC,6LAAC;gBAAI,WAAW,kNAAM,CAAC,cAAc;;kCACnC,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBAAK,WAAW,kNAAM,CAAC,UAAU;;4BAC/B;4BAAc;4BAAK;4BAAW;;;;;;;;;;;;;0BAInC,6LAAC;gBAAI,WAAW,kNAAM,CAAC,eAAe;0BAEnC,CAAC,eAAe,aACf,gBAAgB,aAChB,eAAe,SAAS,mBACxB;8BACE,cAAA,6LAAC;wBAAI,WAAW,kNAAM,CAAC,qBAAqB;;4BACzC,gBAAgB,2BACf,6LAAC;gCAAI,WAAW,kNAAM,CAAC,WAAW;;kDAChC,6LAAC;;4CAAK;0DAEJ,6LAAC,+JAAO;gDACN,SAAU;gDACV,UAAS;0DAET,cAAA,6LAAC;8DACC,cAAA,6LAAC,mKAAc;;;;;;;;;;;;;;;;;;;;;kDAIrB,6LAAC;wCAAK,WAAW,kNAAM,CAAC,KAAK;;4CAAG;4CAAY;;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAW,kNAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,WAAW,kNAAM,CAAC,KAAK;;4CAAE;4CAC3B,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;4BAI9B,wBAAwB,iBAAiB,2BACxC,6LAAC;gCAAI,WAAW,kNAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,WAAW,kNAAM,CAAC,KAAK;;4CAAE;4CAC3B,aAAa,OAAO,CAAC;;;;;;;;;;;;;4BAK5B,wBACC,gBAAgB,aAChB,cAAc,mBACZ,6LAAC;gCAAI,WAAW,kNAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,WAAW,kNAAM,CAAC,KAAK;;4CAAE;4CAC3B,YAAY,OAAO,CAAC;;;;;;;;;;;;;4BAK7B,wBAAwB,eAAe,2BACtC,6LAAC;gCAAI,WAAW,AAAC,GAAwB,OAAtB,kNAAM,CAAC,WAAW,EAAC,KAAoB,OAAjB,kNAAM,CAAC,SAAS;;kDACvD,6LAAC;kDACC,cAAA,6LAAC;sDAAO;;;;;;;;;;;kDAEV,6LAAC;wCAAK,WAAW,kNAAM,CAAC,KAAK;kDAC3B,cAAA,6LAAC;;gDAAO;gDAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU1C,4BACC,6LAAC;gBAAI,WAAW,kNAAM,CAAC,gBAAgB;0BACrC,cAAA,6LAAC;oBAAO,WAAW,kNAAM,CAAC,eAAe;oBAAE,SAAS;8BAAY;;;;;;;;;;;;;;;;;AAO1E;GAnIM;KAAA;uCAqIS", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/TextLimit.tsx"], "sourcesContent": ["interface Props {\r\n  title: string\r\n  maxLength: number\r\n}\r\n\r\nconst LimitTitleLength = ({ title, maxLength }: Props) => {\r\n  function limitTitleLength(title: string, maxLength: number) {\r\n    if (title.length > maxLength) {\r\n      return title.substring(0, maxLength) + '...'\r\n    }\r\n    return title\r\n  }\r\n\r\n  return <>{limitTitleLength(title, maxLength)}</>\r\n}\r\n\r\nexport default LimitTitleLength"], "names": [], "mappings": ";;;;;;AAKA,MAAM,mBAAmB;QAAC,EAAE,KAAK,EAAE,SAAS,EAAS;IACnD,SAAS,iBAAiB,KAAa,EAAE,SAAiB;QACxD,IAAI,MAAM,MAAM,GAAG,WAAW;YAC5B,OAAO,MAAM,SAAS,CAAC,GAAG,aAAa;QACzC;QACA,OAAO;IACT;IAEA,qBAAO;kBAAG,iBAAiB,OAAO;;AACpC;KATM;uCAWS", "debugId": null}}, {"offset": {"line": 1065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/cart-selection-hooks.ts"], "sourcesContent": ["import { useMutation, useQueryClient } from '@tanstack/react-query'\nimport { useCallback } from 'react'\nimport { AxiosError } from 'axios'\nimport APIClient from '../lib/api-client'\nimport cartStore from '../stores/cart-store'\nimport { CACHE_KEY_CART_ITEMS, SIMPLE_CART } from '../constants/constants'\nimport { ErrorResponse } from '../types/types'\n\ninterface CartItemSelectionRequest {\n  is_selected: boolean\n}\n\ninterface BulkSelectionRequest {\n  item_ids?: number[]\n  select_all?: boolean\n  deselect_all?: boolean\n}\n\ninterface SelectionResponse {\n  success: boolean\n  updated_count?: number\n  message: string\n  id?: number\n  is_selected?: boolean\n}\n\nexport const useCartItemSelection = () => {\n  const { cartId } = cartStore()\n  const queryClient = useQueryClient()\n\n  return useMutation<\n    SelectionResponse,\n    AxiosError<ErrorResponse>,\n    { itemId: number; isSelected: boolean }\n  >({\n    mutationFn: async ({ itemId, isSelected }) => {\n      const apiClient = new APIClient<\n        SelectionResponse,\n        CartItemSelectionRequest\n      >(`/cart/${cartId}/items/${itemId}/select/`)\n      return apiClient.patch({ is_selected: isSelected })\n    },\n    // Optimistic updates: apply selection change locally before server responds\n    onMutate: async (variables) => {\n      const { itemId, isSelected } = variables\n      if (!cartId)\n        return {\n          previousCart: null,\n          previousSelected: cartStore.getState().selectedItemIds || [],\n        }\n\n      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)\n      await queryClient.cancelQueries({\n        queryKey: [CACHE_KEY_CART_ITEMS, cartId],\n      })\n\n      const previousCart = queryClient.getQueryData<unknown>([\n        CACHE_KEY_CART_ITEMS,\n        cartId,\n      ])\n\n      // Optimistically update the cached cart items if shape matches { cart_items: Array }\n      if (previousCart && typeof previousCart === 'object') {\n        const prevObj = previousCart as Record<string, unknown>\n        if (Array.isArray(prevObj.cart_items)) {\n          const prev = previousCart as {\n            cart_items: Array<Record<string, unknown>>\n          }\n          const newCart = {\n            ...prev,\n            cart_items: prev.cart_items.map((it) => {\n              // runtime-safe check and shallow update\n              if (it && typeof it === 'object') {\n                const rec = it as Record<string, unknown>\n                if (rec.id !== undefined && Number(rec.id) === itemId) {\n                  return { ...rec, is_selected: isSelected }\n                }\n              }\n              return it\n            }),\n          }\n          queryClient.setQueryData([CACHE_KEY_CART_ITEMS, cartId], newCart)\n        }\n      }\n\n      // Optimistically update local persisted selection array\n      const currentSelection = cartStore.getState().selectedItemIds || []\n      const has = currentSelection.includes(itemId)\n      let newSelection = currentSelection\n      if (isSelected && !has) newSelection = [...currentSelection, itemId]\n      else if (!isSelected && has)\n        newSelection = currentSelection.filter((id) => id !== itemId)\n      cartStore.getState().setSelectedItems(newSelection)\n\n      return { previousCart, previousSelected: currentSelection }\n    },\n    onError: (\n      error: AxiosError<ErrorResponse>,\n      variables: { itemId: number; isSelected: boolean },\n      context: unknown\n    ) => {\n      console.error('Error updating item selection:', error)\n      // rollback cache\n      if (cartId && context && typeof context === 'object') {\n        const ctx = context as {\n          previousCart?: unknown\n          previousSelected?: number[]\n        }\n        if (ctx.previousCart) {\n          queryClient.setQueryData(\n            [CACHE_KEY_CART_ITEMS, cartId],\n            ctx.previousCart\n          )\n        }\n        if (ctx.previousSelected) {\n          cartStore.getState().setSelectedItems(ctx.previousSelected)\n        }\n      }\n    },\n    onSettled: () => {\n      // Only invalidate simple cart cache to avoid triggering expensive useCart queries\n      if (cartId) {\n        queryClient.invalidateQueries({\n          queryKey: [SIMPLE_CART, cartId],\n        })\n      }\n    },\n  })\n}\n\nexport const useBulkCartSelection = () => {\n  const { cartId } = cartStore()\n  const queryClient = useQueryClient()\n\n  const bulkSelectMutation = useMutation<\n    SelectionResponse,\n    AxiosError<ErrorResponse>,\n    BulkSelectionRequest\n  >({\n    mutationFn: async (data) => {\n      const apiClient = new APIClient<SelectionResponse, BulkSelectionRequest>(\n        `/cart/${cartId}/items/bulk-select/`\n      )\n      return apiClient.post(data)\n    },\n    onSuccess: (data, variables) => {\n      // Update local store based on operation\n      if (variables.select_all) {\n        // This will be handled by the parent component that knows all item IDs\n      } else if (variables.item_ids) {\n        cartStore.getState().setSelectedItems(variables.item_ids)\n      }\n\n      // Only invalidate simple cart cache to avoid triggering expensive useCart queries\n      queryClient.invalidateQueries({\n        queryKey: [SIMPLE_CART, cartId],\n      })\n    },\n    onError: (error) => {\n      console.error('Error bulk selecting items:', error)\n    },\n  })\n\n  const bulkDeselectMutation = useMutation<\n    SelectionResponse,\n    AxiosError<ErrorResponse>,\n    BulkSelectionRequest\n  >({\n    mutationFn: async (data) => {\n      const apiClient = new APIClient<SelectionResponse, BulkSelectionRequest>(\n        `/cart/${cartId}/items/bulk-deselect/`\n      )\n      return apiClient.post(data)\n    },\n    onSuccess: (data, variables) => {\n      // Update local store based on operation\n      if (variables.deselect_all) {\n        cartStore.getState().clearSelection()\n      } else if (variables.item_ids) {\n        // Remove specific items from selection\n        const currentSelection = cartStore.getState().selectedItemIds || []\n        const newSelection = currentSelection.filter(\n          (id) => !variables.item_ids?.includes(id)\n        )\n        cartStore.getState().setSelectedItems(newSelection)\n      }\n\n      // Only invalidate simple cart cache to avoid triggering expensive useCart queries\n      queryClient.invalidateQueries({\n        queryKey: [SIMPLE_CART, cartId],\n      })\n    },\n    onError: (error) => {\n      console.error('Error bulk deselecting items:', error)\n    },\n  })\n\n  return {\n    bulkSelect: bulkSelectMutation.mutate,\n    bulkDeselect: bulkDeselectMutation.mutate,\n    isSelectLoading: bulkSelectMutation.isPending,\n    isDeselectLoading: bulkDeselectMutation.isPending,\n    selectError: bulkSelectMutation.error,\n    deselectError: bulkDeselectMutation.error,\n  }\n}\n\nexport const useSelectedCartSummary = () => {\n  const { cartId } = cartStore()\n\n  const fetchSummary = useCallback(() => {\n    const apiClient = new APIClient<{\n      selected_items_count: number\n      total_items_count: number\n      selected_total_price: number\n      selected_total_weight: number\n      all_items_selected: boolean\n      selected_item_ids: number[]\n    }>(`/cart/${cartId}/selected_summary/`)\n\n    return apiClient.get()\n  }, [cartId])\n\n  return { fetchSummary }\n}\n\n// Hook to sync frontend selection state with backend\n// export const useSyncCartSelection = () => {\n//   // avoid subscribing to the store here so the returned function identity is stable\n//   const getSelected = () => cartStore.getState().selectedItemIds || []\n//   const setSelected = (ids: number[]) =>\n//     cartStore.getState().setSelectedItems(ids)\n\n//   const syncWithBackend = useCallback(\n//     (cartItems: Array<{ id: number; is_selected?: boolean }>) => {\n//       // Compute backend selected ids\n//       const backendSelectedIds = cartItems\n//         .filter((item) => item.is_selected)\n//         .map((item) => item.id)\n\n//       // Compare lengths first for a cheap check, then confirm all ids match (order-insensitive)\n//       const current = getSelected()\n//       if (\n//         backendSelectedIds.length === current.length &&\n//         backendSelectedIds.every((id) => current.includes(id))\n//       ) {\n//         // No change -> avoid calling setSelected to prevent triggering effects\n//         return\n//       }\n\n//       // Only update when backend selection differs from current\n//       setSelected(backendSelectedIds)\n//     },\n//     []\n//   )\n\n//   return { syncWithBackend }\n// }\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AAqBO,MAAM,uBAAuB;;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,4IAAS;IAC5B,MAAM,cAAc,IAAA,2MAAc;IAElC,OAAO,IAAA,gMAAW,EAIhB;QACA,UAAU;gDAAE;oBAAO,EAAE,MAAM,EAAE,UAAU,EAAE;gBACvC,MAAM,YAAY,IAAI,yIAAS,CAG7B,AAAC,SAAwB,OAAhB,QAAO,WAAgB,OAAP,QAAO;gBAClC,OAAO,UAAU,KAAK,CAAC;oBAAE,aAAa;gBAAW;YACnD;;QACA,4EAA4E;QAC5E,QAAQ;gDAAE,OAAO;gBACf,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;gBAC/B,IAAI,CAAC,QACH,OAAO;oBACL,cAAc;oBACd,kBAAkB,4IAAS,CAAC,QAAQ,GAAG,eAAe,IAAI,EAAE;gBAC9D;gBAEF,gFAAgF;gBAChF,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU;wBAAC,wJAAoB;wBAAE;qBAAO;gBAC1C;gBAEA,MAAM,eAAe,YAAY,YAAY,CAAU;oBACrD,wJAAoB;oBACpB;iBACD;gBAED,qFAAqF;gBACrF,IAAI,gBAAgB,OAAO,iBAAiB,UAAU;oBACpD,MAAM,UAAU;oBAChB,IAAI,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;wBACrC,MAAM,OAAO;wBAGb,MAAM,UAAU;4BACd,GAAG,IAAI;4BACP,YAAY,KAAK,UAAU,CAAC,GAAG;oEAAC,CAAC;oCAC/B,wCAAwC;oCACxC,IAAI,MAAM,OAAO,OAAO,UAAU;wCAChC,MAAM,MAAM;wCACZ,IAAI,IAAI,EAAE,KAAK,aAAa,OAAO,IAAI,EAAE,MAAM,QAAQ;4CACrD,OAAO;gDAAE,GAAG,GAAG;gDAAE,aAAa;4CAAW;wCAC3C;oCACF;oCACA,OAAO;gCACT;;wBACF;wBACA,YAAY,YAAY,CAAC;4BAAC,wJAAoB;4BAAE;yBAAO,EAAE;oBAC3D;gBACF;gBAEA,wDAAwD;gBACxD,MAAM,mBAAmB,4IAAS,CAAC,QAAQ,GAAG,eAAe,IAAI,EAAE;gBACnE,MAAM,MAAM,iBAAiB,QAAQ,CAAC;gBACtC,IAAI,eAAe;gBACnB,IAAI,cAAc,CAAC,KAAK,eAAe;uBAAI;oBAAkB;iBAAO;qBAC/D,IAAI,CAAC,cAAc,KACtB,eAAe,iBAAiB,MAAM;wDAAC,CAAC,KAAO,OAAO;;gBACxD,4IAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;gBAEtC,OAAO;oBAAE;oBAAc,kBAAkB;gBAAiB;YAC5D;;QACA,OAAO;gDAAE,CACP,OACA,WACA;gBAEA,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,iBAAiB;gBACjB,IAAI,UAAU,WAAW,OAAO,YAAY,UAAU;oBACpD,MAAM,MAAM;oBAIZ,IAAI,IAAI,YAAY,EAAE;wBACpB,YAAY,YAAY,CACtB;4BAAC,wJAAoB;4BAAE;yBAAO,EAC9B,IAAI,YAAY;oBAEpB;oBACA,IAAI,IAAI,gBAAgB,EAAE;wBACxB,4IAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC,IAAI,gBAAgB;oBAC5D;gBACF;YACF;;QACA,SAAS;gDAAE;gBACT,kFAAkF;gBAClF,IAAI,QAAQ;oBACV,YAAY,iBAAiB,CAAC;wBAC5B,UAAU;4BAAC,+IAAW;4BAAE;yBAAO;oBACjC;gBACF;YACF;;IACF;AACF;GAtGa;;QAES,2MAAc;QAE3B,gMAAW;;;AAoGb,MAAM,uBAAuB;;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,4IAAS;IAC5B,MAAM,cAAc,IAAA,2MAAc;IAElC,MAAM,qBAAqB,IAAA,gMAAW,EAIpC;QACA,UAAU;oEAAE,OAAO;gBACjB,MAAM,YAAY,IAAI,yIAAS,CAC7B,AAAC,SAAe,OAAP,QAAO;gBAElB,OAAO,UAAU,IAAI,CAAC;YACxB;;QACA,SAAS;oEAAE,CAAC,MAAM;gBAChB,wCAAwC;gBACxC,IAAI,UAAU,UAAU,EAAE;gBACxB,uEAAuE;gBACzE,OAAO,IAAI,UAAU,QAAQ,EAAE;oBAC7B,4IAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC,UAAU,QAAQ;gBAC1D;gBAEA,kFAAkF;gBAClF,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC,+IAAW;wBAAE;qBAAO;gBACjC;YACF;;QACA,OAAO;oEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;;IACF;IAEA,MAAM,uBAAuB,IAAA,gMAAW,EAItC;QACA,UAAU;sEAAE,OAAO;gBACjB,MAAM,YAAY,IAAI,yIAAS,CAC7B,AAAC,SAAe,OAAP,QAAO;gBAElB,OAAO,UAAU,IAAI,CAAC;YACxB;;QACA,SAAS;sEAAE,CAAC,MAAM;gBAChB,wCAAwC;gBACxC,IAAI,UAAU,YAAY,EAAE;oBAC1B,4IAAS,CAAC,QAAQ,GAAG,cAAc;gBACrC,OAAO,IAAI,UAAU,QAAQ,EAAE;oBAC7B,uCAAuC;oBACvC,MAAM,mBAAmB,4IAAS,CAAC,QAAQ,GAAG,eAAe,IAAI,EAAE;oBACnE,MAAM,eAAe,iBAAiB,MAAM;+FAC1C,CAAC;gCAAQ;mCAAD,GAAC,sBAAA,UAAU,QAAQ,cAAlB,0CAAA,oBAAoB,QAAQ,CAAC;;;oBAExC,4IAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;gBACxC;gBAEA,kFAAkF;gBAClF,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC,+IAAW;wBAAE;qBAAO;gBACjC;YACF;;QACA,OAAO;sEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,iCAAiC;YACjD;;IACF;IAEA,OAAO;QACL,YAAY,mBAAmB,MAAM;QACrC,cAAc,qBAAqB,MAAM;QACzC,iBAAiB,mBAAmB,SAAS;QAC7C,mBAAmB,qBAAqB,SAAS;QACjD,aAAa,mBAAmB,KAAK;QACrC,eAAe,qBAAqB,KAAK;IAC3C;AACF;IA3Ea;;QAES,2MAAc;QAEP,gMAAW;QA6BT,gMAAW;;;AA4CnC,MAAM,yBAAyB;;IACpC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,4IAAS;IAE5B,MAAM,eAAe,IAAA,4KAAW;4DAAC;YAC/B,MAAM,YAAY,IAAI,yIAAS,CAO5B,AAAC,SAAe,OAAP,QAAO;YAEnB,OAAO,UAAU,GAAG;QACtB;2DAAG;QAAC;KAAO;IAEX,OAAO;QAAE;IAAa;AACxB,EAEA,qDAAqD;CACrD,8CAA8C;CAC9C,uFAAuF;CACvF,yEAAyE;CACzE,2CAA2C;CAC3C,iDAAiD;CAEjD,yCAAyC;CACzC,qEAAqE;CACrE,wCAAwC;CACxC,6CAA6C;CAC7C,8CAA8C;CAC9C,kCAAkC;CAElC,mGAAmG;CACnG,sCAAsC;CACtC,aAAa;CACb,0DAA0D;CAC1D,iEAAiE;CACjE,YAAY;CACZ,kFAAkF;CAClF,iBAAiB;CACjB,UAAU;CAEV,mEAAmE;CACnE,wCAAwC;CACxC,SAAS;CACT,SAAS;CACT,MAAM;CAEN,+BAA+B;CAC/B,IAAI;;IAlDS", "debugId": null}}, {"offset": {"line": 1342, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart_item\": \"CartItemsList-module-scss-module__kultEW__cart_item\",\n  \"cart_item__extra_data\": \"CartItemsList-module-scss-module__kultEW__cart_item__extra_data\",\n  \"cart_item__img\": \"CartItemsList-module-scss-module__kultEW__cart_item__img\",\n  \"cart_item__info\": \"CartItemsList-module-scss-module__kultEW__cart_item__info\",\n  \"cart_item__quantity\": \"CartItemsList-module-scss-module__kultEW__cart_item__quantity\",\n  \"cart_item__title\": \"CartItemsList-module-scss-module__kultEW__cart_item__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/cart/CartItemsList.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\n\r\nimport { <PERSON><PERSON><PERSON>, FiMinus, FiTrash2 } from 'react-icons/fi'\r\nimport Image from 'next/image'\r\nimport Link from 'next/link'\r\nimport LimitTitleLength from '@/src/components/utils/TextLimit'\r\nimport { CartItemShape } from '@/src/types/store-types'\r\nimport {\r\n  useCartItemSelection,\r\n  useBulkCartSelection,\r\n} from '@/src/hooks/cart-selection-hooks'\r\nimport cartStore from '@/src/stores/cart-store'\r\nimport styles from './CartItemsList.module.scss'\r\n\r\ninterface CartItemsListProps {\r\n  cartItems: CartItemShape[]\r\n  handleIncrement?: (item: CartItemShape) => void\r\n  handleDecrement?: (item: CartItemShape) => void\r\n  deleteCartItem?: (itemId: number) => void\r\n  showSelection?: boolean\r\n  // optional server-provided selection set (server as source-of-truth)\r\n  selectedIds?: Set<number>\r\n}\r\n\r\nconst CartItemsList = ({\r\n  cartItems,\r\n  handleIncrement,\r\n  handleDecrement,\r\n  deleteCartItem,\r\n  showSelection = true,\r\n  selectedIds,\r\n}: CartItemsListProps) => {\r\n  // local persisted array (fallback when server doesn't provide selection)\r\n  const storeSelectedItemIds = cartStore((s) => s.selectedItemIds)\r\n  const toggleSelectAll = cartStore((s) => s.toggleSelectAll)\r\n  const { mutate: updateItemSelection } = useCartItemSelection()\r\n  const { bulkSelect, bulkDeselect } = useBulkCartSelection()\r\n\r\n  // Build a local Set for O(1) lookups from persisted array\r\n  // Prefer server-provided selection when available, otherwise use persisted array\r\n  const safeSelectedCartItems = React.useMemo(() => {\r\n    if (selectedIds && selectedIds instanceof Set)\r\n      return new Set<number>(selectedIds)\r\n    return new Set<number>(\r\n      Array.isArray(storeSelectedItemIds) ? storeSelectedItemIds : []\r\n    )\r\n  }, [selectedIds, storeSelectedItemIds])\r\n\r\n  const handleItemSelectionChange = (\r\n    itemId: number,\r\n    currentlySelected: boolean\r\n  ) => {\r\n    // Update backend\r\n    updateItemSelection({\r\n      itemId,\r\n      isSelected: !currentlySelected,\r\n    })\r\n  }\r\n\r\n  const handleSelectAllChange = () => {\r\n    const allItemIds = cartItems.map((item) => item.id)\r\n    const allSelected = allItemIds.every((id) => safeSelectedCartItems.has(id))\r\n\r\n    if (allSelected) {\r\n      // Deselect all\r\n      bulkDeselect({ deselect_all: true })\r\n      // when server is source-of-truth we avoid mutating local store\r\n      if (!selectedIds) toggleSelectAll(allItemIds)\r\n    } else {\r\n      // Select all\r\n      bulkSelect({ select_all: true, item_ids: allItemIds })\r\n      if (!selectedIds) toggleSelectAll(allItemIds)\r\n    }\r\n  }\r\n\r\n  const allItemsSelected =\r\n    cartItems.length > 0 &&\r\n    cartItems.every((item) => safeSelectedCartItems.has(item.id))\r\n  const someItemsSelected = cartItems.some((item) =>\r\n    safeSelectedCartItems.has(item.id)\r\n  )\r\n\r\n  return (\r\n    <div>\r\n      {showSelection && cartItems.length > 0 && (\r\n        <div className={styles.selection_header}>\r\n          <label className={styles.select_all_container}>\r\n            <input\r\n              type='checkbox'\r\n              checked={allItemsSelected}\r\n              ref={(input) => {\r\n                if (input)\r\n                  input.indeterminate = someItemsSelected && !allItemsSelected\r\n              }}\r\n              onChange={handleSelectAllChange}\r\n              className={styles.select_all_checkbox}\r\n            />\r\n            <span>Select All ({cartItems.length} items)</span>\r\n          </label>\r\n        </div>\r\n      )}\r\n\r\n      <ul>\r\n        {cartItems?.map((item: CartItemShape) => {\r\n          const isSelected = safeSelectedCartItems.has(item.id)\r\n\r\n          return (\r\n            <li\r\n              key={item.id}\r\n              className={`${styles.cart_item} ${\r\n                isSelected ? styles.selected : ''\r\n              }`}\r\n            >\r\n              {showSelection && (\r\n                <div className={styles.cart_item__selection}>\r\n                  <input\r\n                    type='checkbox'\r\n                    checked={isSelected}\r\n                    onChange={() =>\r\n                      handleItemSelectionChange(item.id, isSelected)\r\n                    }\r\n                    className={styles.item_checkbox}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              <div className={styles.cart_item__img}>\r\n                <Image\r\n                  src={\r\n                    item.product_variant?.product_image?.[0]?.image\r\n                      ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`\r\n                      : ''\r\n                  }\r\n                  alt={\r\n                    item.product_variant?.product_image?.[0]\r\n                      ?.alternative_text || item.product.title\r\n                  }\r\n                  width={200}\r\n                  height={200}\r\n                />\r\n              </div>\r\n\r\n              <div className={styles.cart_item__info}>\r\n                <span className={styles.cart_item__title}>\r\n                  <Link href={`/product/${item.product.slug}`}>\r\n                    <LimitTitleLength\r\n                      title={item.product.title}\r\n                      maxLength={60}\r\n                    />\r\n                  </Link>\r\n                </span>\r\n                {` `}\r\n                <span>\r\n                  (${item.product_variant.price} x {item.quantity})\r\n                </span>\r\n                <span>\r\n                  Variant: {item.product_variant?.price_label?.attribute_value}\r\n                </span>\r\n                {Object.entries(item.extra_data).map(([key, value], index) => (\r\n                  <div key={index} className={styles.cart_item__extra_data}>\r\n                    <p>{key} :</p>\r\n                    <p>{value}</p>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className={styles.cart_item__quantity}>\r\n                <div>\r\n                  <p>Qty:</p>\r\n                  {handleDecrement && (\r\n                    <button\r\n                      onClick={() => handleDecrement(item)}\r\n                      disabled={item.product_variant.stock_qty === 0}\r\n                    >\r\n                      <i>\r\n                        <FiMinus />\r\n                      </i>\r\n                    </button>\r\n                  )}\r\n                  <p>{item.quantity}</p>\r\n                  {handleIncrement && (\r\n                    <button\r\n                      onClick={() => handleIncrement(item)}\r\n                      disabled={item.product_variant.stock_qty === 0}\r\n                    >\r\n                      <i>\r\n                        <FiPlus />\r\n                      </i>\r\n                    </button>\r\n                  )}\r\n                  {deleteCartItem && (\r\n                    <button onClick={() => deleteCartItem(item.id)}>\r\n                      <i>\r\n                        <FiTrash2 />\r\n                      </i>\r\n                    </button>\r\n                  )}\r\n                </div>\r\n                {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}\r\n              </div>\r\n            </li>\r\n          )\r\n        })}\r\n      </ul>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CartItemsList\r\n"], "names": [], "mappings": ";;;;AAoI2B;;AAlI3B;AAEA;AACA;AACA;AACA;AAEA;AAIA;AACA;;;AAdA;;;;;;;;;AA0BA,MAAM,gBAAgB;QAAC,EACrB,SAAS,EACT,eAAe,EACf,eAAe,EACf,cAAc,EACd,gBAAgB,IAAI,EACpB,WAAW,EACQ;;IACnB,yEAAyE;IACzE,MAAM,uBAAuB,IAAA,4IAAS,EAAC,CAAC,IAAM,EAAE,eAAe;IAC/D,MAAM,kBAAkB,IAAA,4IAAS,EAAC,CAAC,IAAM,EAAE,eAAe;IAC1D,MAAM,EAAE,QAAQ,mBAAmB,EAAE,GAAG,IAAA,qKAAoB;IAC5D,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,IAAA,qKAAoB;IAEzD,0DAA0D;IAC1D,iFAAiF;IACjF,MAAM,wBAAwB,wKAAK,CAAC,OAAO;wDAAC;YAC1C,IAAI,eAAe,uBAAuB,KACxC,OAAO,IAAI,IAAY;YACzB,OAAO,IAAI,IACT,MAAM,OAAO,CAAC,wBAAwB,uBAAuB,EAAE;QAEnE;uDAAG;QAAC;QAAa;KAAqB;IAEtC,MAAM,4BAA4B,CAChC,QACA;QAEA,iBAAiB;QACjB,oBAAoB;YAClB;YACA,YAAY,CAAC;QACf;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,aAAa,UAAU,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;QAClD,MAAM,cAAc,WAAW,KAAK,CAAC,CAAC,KAAO,sBAAsB,GAAG,CAAC;QAEvE,IAAI,aAAa;YACf,eAAe;YACf,aAAa;gBAAE,cAAc;YAAK;YAClC,+DAA+D;YAC/D,IAAI,CAAC,aAAa,gBAAgB;QACpC,OAAO;YACL,aAAa;YACb,WAAW;gBAAE,YAAY;gBAAM,UAAU;YAAW;YACpD,IAAI,CAAC,aAAa,gBAAgB;QACpC;IACF;IAEA,MAAM,mBACJ,UAAU,MAAM,GAAG,KACnB,UAAU,KAAK,CAAC,CAAC,OAAS,sBAAsB,GAAG,CAAC,KAAK,EAAE;IAC7D,MAAM,oBAAoB,UAAU,IAAI,CAAC,CAAC,OACxC,sBAAsB,GAAG,CAAC,KAAK,EAAE;IAGnC,qBACE,6LAAC;;YACE,iBAAiB,UAAU,MAAM,GAAG,mBACnC,6LAAC;gBAAI,WAAW,4MAAM,CAAC,gBAAgB;0BACrC,cAAA,6LAAC;oBAAM,WAAW,4MAAM,CAAC,oBAAoB;;sCAC3C,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,KAAK,CAAC;gCACJ,IAAI,OACF,MAAM,aAAa,GAAG,qBAAqB,CAAC;4BAChD;4BACA,UAAU;4BACV,WAAW,4MAAM,CAAC,mBAAmB;;;;;;sCAEvC,6LAAC;;gCAAK;gCAAa,UAAU,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC;0BACE,sBAAA,gCAAA,UAAW,GAAG,CAAC,CAAC;wBA0BL,sCAAA,qCAAA,uBAKA,uCAAA,sCAAA,wBAsBQ,mCAAA;oBApDlB,MAAM,aAAa,sBAAsB,GAAG,CAAC,KAAK,EAAE;oBAEpD,qBACE,6LAAC;wBAEC,WAAW,AAAC,GACV,OADY,4MAAM,CAAC,SAAS,EAAC,KAE9B,OADC,aAAa,4MAAM,CAAC,QAAQ,GAAG;;4BAGhC,+BACC,6LAAC;gCAAI,WAAW,4MAAM,CAAC,oBAAoB;0CACzC,cAAA,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,IACR,0BAA0B,KAAK,EAAE,EAAE;oCAErC,WAAW,4MAAM,CAAC,aAAa;;;;;;;;;;;0CAKrC,6LAAC;gCAAI,WAAW,4MAAM,CAAC,cAAc;0CACnC,cAAA,6LAAC,2IAAK;oCACJ,KACE,EAAA,wBAAA,KAAK,eAAe,cAApB,6CAAA,sCAAA,sBAAsB,aAAa,cAAnC,2DAAA,uCAAA,mCAAqC,CAAC,EAAE,cAAxC,2DAAA,qCAA0C,KAAK,IAC3C,AAAC,GAA4C,gFAAH,KAA+C,OAA5C,KAAK,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,IACxF;oCAEN,KACE,EAAA,yBAAA,KAAK,eAAe,cAApB,8CAAA,uCAAA,uBAAsB,aAAa,cAAnC,4DAAA,wCAAA,oCAAqC,CAAC,EAAE,cAAxC,4DAAA,sCACI,gBAAgB,KAAI,KAAK,OAAO,CAAC,KAAK;oCAE5C,OAAO;oCACP,QAAQ;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,WAAW,4MAAM,CAAC,eAAe;;kDACpC,6LAAC;wCAAK,WAAW,4MAAM,CAAC,gBAAgB;kDACtC,cAAA,6LAAC,0KAAI;4CAAC,MAAM,AAAC,YAA6B,OAAlB,KAAK,OAAO,CAAC,IAAI;sDACvC,cAAA,6LAAC,sJAAgB;gDACf,OAAO,KAAK,OAAO,CAAC,KAAK;gDACzB,WAAW;;;;;;;;;;;;;;;;oCAIf;kDACF,6LAAC;;4CAAK;4CACD,KAAK,eAAe,CAAC,KAAK;4CAAC;4CAAI,KAAK,QAAQ;4CAAC;;;;;;;kDAElD,6LAAC;;4CAAK;6CACM,yBAAA,KAAK,eAAe,cAApB,8CAAA,oCAAA,uBAAsB,WAAW,cAAjC,wDAAA,kCAAmC,eAAe;;;;;;;oCAE7D,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,QAAe;4CAAd,CAAC,KAAK,MAAM;6DAChD,6LAAC;4CAAgB,WAAW,4MAAM,CAAC,qBAAqB;;8DACtD,6LAAC;;wDAAG;wDAAI;;;;;;;8DACR,6LAAC;8DAAG;;;;;;;2CAFI;;;;;;;;;;;;0CAOd,6LAAC;gCAAI,WAAW,4MAAM,CAAC,mBAAmB;;kDACxC,6LAAC;;0DACC,6LAAC;0DAAE;;;;;;4CACF,iCACC,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;0DAE7C,cAAA,6LAAC;8DACC,cAAA,6LAAC,4JAAO;;;;;;;;;;;;;;;0DAId,6LAAC;0DAAG,KAAK,QAAQ;;;;;;4CAChB,iCACC,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;0DAE7C,cAAA,6LAAC;8DACC,cAAA,6LAAC,2JAAM;;;;;;;;;;;;;;;4CAIZ,gCACC,6LAAC;gDAAO,SAAS,IAAM,eAAe,KAAK,EAAE;0DAC3C,cAAA,6LAAC;8DACC,cAAA,6LAAC,6JAAQ;;;;;;;;;;;;;;;;;;;;;oCAKhB,KAAK,eAAe,CAAC,SAAS,KAAK,mBAAK,6LAAC;kDAAE;;;;;;;;;;;;;uBA1FzC,KAAK,EAAE;;;;;gBA8FlB;;;;;;;;;;;;AAIR;GAtLM;;QAWoC,qKAAoB;QACvB,qKAAoB;;;KAZrD;uCAwLS", "debugId": null}}, {"offset": {"line": 1723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/checkout/payment-choice/page.tsx"], "sourcesContent": ["'use client'\n\nimport Al<PERSON> from '@/src/components/utils/alert/Alert'\nimport EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'\nimport { useCart, useShippingCalculation } from '@/src/hooks/cart-hooks'\nimport { usePaymentOptions } from '@/src/hooks/checkout-hooks'\nimport { useCustomerDetails } from '@/src/hooks/customer-hooks'\nimport { useCreateOrder } from '@/src/hooks/order-hooks'\nimport authStore from '@/src/stores/auth-store'\nimport cartStore from '@/src/stores/cart-store'\nimport { PaymentOptionsShape } from '@/src/types/types'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport styles from './PaymentChoice.module.scss'\nimport ButtonState from '@/src/components/utils/button-state/ButtonState'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport SelectedCartSummary from '../../components/cart/SelectedCartSummary'\nimport CartItemsList from '../../components/cart/CartItemsList'\n\nconst PaymentChoice = () => {\n  const router = useRouter()\n  const { isLoggedIn } = authStore()\n  const { data: customer } = useCustomerDetails(isLoggedIn)\n  const {\n    cartId,\n    setSelectedPaymentOption,\n    selectedAddress,\n    selectedPaymentOption,\n  } = cartStore()\n  const { isLoading, error, data } = useCart()\n\n  const { createOrder } = useCreateOrder()\n  const payOptions = usePaymentOptions()\n  const shippingCalculation = useShippingCalculation()\n\n  // FOR TESTING UI ONLY: Force isPending to true to see loading spinner\n  // createOrder.isPending = true\n\n  console.log(payOptions.data)\n\n  useEffect(() => {\n    if (!isLoggedIn) {\n      router.push('/login')\n    }\n  }, [isLoggedIn, router])\n\n  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {\n    setSelectedPaymentOption(paymentOption)\n  }\n\n  useEffect(() => {\n    if (\n      payOptions.data &&\n      payOptions.data.length > 0 &&\n      !selectedPaymentOption\n    ) {\n      setSelectedPaymentOption(payOptions.data[0])\n    }\n  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])\n\n  // Calculate shipping when component loads and when selected address changes\n  // Calculate shipping when component loads and when selected address changes\n  useEffect(() => {\n    if (cartId && selectedAddress?.id && data && data.cart_items?.length > 0) {\n      // Only calculate if shipping hasn't been calculated yet\n      if (!data.shipping_cost && !shippingCalculation.isPending) {\n        shippingCalculation.calculateShipping({\n          destination_address_id: selectedAddress.id,\n          get_all_options: false,\n        })\n      }\n    }\n  }, [\n    cartId,\n    selectedAddress?.id,\n    data?.cart_items?.length,\n    // data?.shipping_cost, // Remove this from dependencies\n    shippingCalculation.isPending, // Add this to prevent concurrent calls\n    shippingCalculation.calculateShipping,\n    data,\n    shippingCalculation,\n  ])\n\n  // useEffect(() => {\n  //   if (shippingCalculation.isSuccess && cartId) {\n  //     refetch()\n  //   }\n  // }, [shippingCalculation.isSuccess, cartId, refetch])\n\n  console.log(customer)\n  console.log(selectedAddress)\n  console.log(selectedPaymentOption)\n\n  // Testing of shipping calculation Pending state (leftover test code removed)\n\n  const createOrderFn = () => {\n    if (\n      window.confirm(\n        'Payment Method or other order details cannot be changed after placing the order.\\n' +\n          'Make sure you have selected the correct payment method and other order details before placing the order.\\n' +\n          'Click OK to place the order or Cancel to go back and make changes.'\n      )\n    ) {\n      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {\n        // Check if shipping calculation is required\n        if (!data?.shipping_cost) {\n          if (shippingCalculation.isPending) {\n            alert(\n              'Shipping calculation is in progress. Please wait a moment and try again.'\n            )\n            return\n          } else {\n            // Calculate shipping first\n            shippingCalculation.calculateShipping({\n              destination_address_id: selectedAddress.id,\n              get_all_options: false,\n            })\n            alert('Calculating shipping costs, please try again in a moment.')\n            return\n          }\n        }\n\n        // Shipping is calculated, proceed with order creation\n        const serverSelectedIds = new Set<number>(\n          Array.isArray(data?.cart_items)\n            ? data.cart_items\n                .filter((it) => it?.is_selected === true)\n                .map((it) => Number(it.id))\n            : []\n        )\n\n        const selectedItemIdsArray: number[] = Array.from(\n          serverSelectedIds\n        ).map((id) => Number(id))\n\n        createOrder.mutate(\n          {\n            cart_id: cartId!,\n            delivery_status: 'Pending',\n            selected_address: selectedAddress.id,\n            payment_method: selectedPaymentOption.id,\n            selected_cart_item_ids:\n              selectedItemIdsArray && selectedItemIdsArray.length > 0\n                ? selectedItemIdsArray\n                : undefined,\n          },\n          {\n            onSuccess: (data) => {\n              router.push(`/checkout/order/${data.id}`)\n            },\n          }\n        )\n      }\n    }\n  }\n\n  return (\n    <>\n      {!cartId ? (\n        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />\n      ) : isLoading ? (\n        <Spinner loading={true} size={20} color='#0091CF' spinnerType='clip' />\n      ) : error ? (\n        <Alert variant='error' message={error.message} />\n      ) : !data ||\n        data?.cart_items?.length === 0 ||\n        Object.keys(data).length === 0 ? (\n        <div className={styles.empty_cart}>\n          <p>Your cart is empty. Add some products to the cart to checkout!</p>\n          <Link href='/'>Go Shopping </Link>\n        </div>\n      ) : (\n        <>\n          <div className='container'>\n            <h3 className={styles.place_order}>Place Order</h3>\n\n            {shippingCalculation.isPending && (\n              <Alert variant='info' message='Calculating shipping costs...' />\n            )}\n\n            {data?.shipping_cost && (\n              <Alert\n                variant='success'\n                message={`Shipping cost calculated successfully! Estimated delivery: ${\n                  data.packing_details?.calculated_at\n                    ? new Date(\n                        data.packing_details.calculated_at\n                      ).toLocaleDateString()\n                    : 'N/A'\n                }`}\n              />\n            )}\n\n            <div className={styles.payment_options_stage}>\n              <section>\n                <section className={styles.contact_info}>\n                  <div className={styles.contact_details}>\n                    <h3>Contact Details: </h3>\n                    <p>\n                      Deliver to: {customer?.first_name} {customer?.last_name}\n                    </p>\n                    <p>Phone: {customer?.phone_number}</p>\n                    <p>Email to: {customer?.email}</p>\n                  </div>\n                  <div className={styles.shipping_address}>\n                    <h3>Shipping address: </h3>\n                    <address>\n                      {selectedAddress?.full_name},<br />\n                      {selectedAddress?.street_name},<br />\n                      {selectedAddress?.postal_code},<br />\n                      {selectedAddress?.city_or_village}\n                      <br />\n                    </address>\n                  </div>\n                </section>\n                <hr />\n                <div className={styles.cart}>\n                  <CartItemsList cartItems={data.cart_items} />\n                </div>\n                <hr />\n\n                {shippingCalculation.error && (\n                  <div>\n                    <Alert\n                      variant='error'\n                      message={`Shipping calculation failed: ${shippingCalculation.error.message}`}\n                    />\n                    <button\n                      className={`${styles.retry_shipping_calc} empty_btn`}\n                      onClick={() =>\n                        selectedAddress?.id &&\n                        shippingCalculation.calculateShipping({\n                          destination_address_id: selectedAddress.id,\n                          get_all_options: false,\n                        })\n                      }\n                      // style={{\n                      //   marginTop: '10px',\n                      //   padding: '8px 16px',\n                      //   backgroundColor: '#0091CF',\n                      //   color: 'white',\n                      //   border: 'none',\n                      //   borderRadius: '4px',\n                      //   cursor: 'pointer',\n                      // }}\n                    >\n                      Retry Shipping Calculation\n                    </button>\n                  </div>\n                )}\n\n                <section>\n                  <div className={styles.payment_options}>\n                    <h3>Payment Method:</h3>\n                    {payOptions?.isPending ? (\n                      <Alert\n                        variant='info'\n                        message='Payment options are loading'\n                      />\n                    ) : payOptions?.error ? (\n                      <Alert\n                        variant='error'\n                        message={payOptions.error.message}\n                      />\n                    ) : payOptions?.data?.length === 0 ? (\n                      <Alert\n                        variant='error'\n                        message='No payment options available'\n                      />\n                    ) : (\n                      <>\n                        {payOptions?.data?.map(\n                          (option: PaymentOptionsShape) => (\n                            <div key={option.id}>\n                              <input\n                                type='radio'\n                                id={`payment-${option.id}`}\n                                name='payment-option'\n                                checked={\n                                  selectedPaymentOption?.id === option.id\n                                }\n                                onChange={() =>\n                                  handlePaymentOptionChange(option)\n                                }\n                              />\n                              <label htmlFor={`payment-${option.id}`}>\n                                {option.name}\n                              </label>\n                            </div>\n                          )\n                        )}\n                      </>\n                    )}\n                  </div>\n                </section>\n              </section>\n              <section className={styles.price_summary}>\n                <SelectedCartSummary\n                  cartItems={data.cart_items}\n                  totalPrice={data?.total_price}\n                  shippingCost={data?.shipping_cost}\n                  packingCost={data?.packing_cost}\n                  grandTotal={data?.grand_total}\n                  item_count={data?.item_count}\n                  cart_weight={data?.cart_weight}\n                  isShippingCalculated={!!data?.shipping_cost}\n                />\n                <button\n                  type='submit'\n                  disabled={\n                    createOrder.isPending || shippingCalculation.isPending\n                  }\n                  onClick={createOrderFn}\n                >\n                  <ButtonState\n                    isLoading={\n                      createOrder.isPending || shippingCalculation.isPending\n                    }\n                    loadingText={\n                      shippingCalculation.isPending\n                        ? 'Calculating Shipping Cost...'\n                        : 'Placing the Order...'\n                    }\n                    buttonText='Place Order'\n                    spinnerSize={16}\n                    spinnerColor='#fff'\n                    spinnerType='clip'\n                  />\n                </button>\n              </section>\n            </div>\n          </div>\n        </>\n      )}\n    </>\n  )\n}\n\nexport default PaymentChoice\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;;;;;;AAoBA,MAAM,gBAAgB;QAwDlB,kBA0FI,mBAmBU,uBAgFM,kBAOC;;IA3PvB,MAAM,SAAS,IAAA,kJAAS;IACxB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,4IAAS;IAChC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,IAAA,0JAAkB,EAAC;IAC9C,MAAM,EACJ,MAAM,EACN,wBAAwB,EACxB,eAAe,EACf,qBAAqB,EACtB,GAAG,IAAA,4IAAS;IACb,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,2IAAO;IAE1C,MAAM,EAAE,WAAW,EAAE,GAAG,IAAA,mJAAc;IACtC,MAAM,aAAa,IAAA,yJAAiB;IACpC,MAAM,sBAAsB,IAAA,0JAAsB;IAElD,sEAAsE;IACtE,+BAA+B;IAE/B,QAAQ,GAAG,CAAC,WAAW,IAAI;IAE3B,IAAA,0KAAS;mCAAC;YACR,IAAI,CAAC,YAAY;gBACf,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAY;KAAO;IAEvB,MAAM,4BAA4B,CAAC;QACjC,yBAAyB;IAC3B;IAEA,IAAA,0KAAS;mCAAC;YACR,IACE,WAAW,IAAI,IACf,WAAW,IAAI,CAAC,MAAM,GAAG,KACzB,CAAC,uBACD;gBACA,yBAAyB,WAAW,IAAI,CAAC,EAAE;YAC7C;QACF;kCAAG;QAAC,WAAW,IAAI;QAAE;QAAuB;KAAyB;IAErE,4EAA4E;IAC5E,4EAA4E;IAC5E,IAAA,0KAAS;mCAAC;gBACqC;YAA7C,IAAI,WAAU,4BAAA,sCAAA,gBAAiB,EAAE,KAAI,QAAQ,EAAA,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,MAAM,IAAG,GAAG;gBACxE,wDAAwD;gBACxD,IAAI,CAAC,KAAK,aAAa,IAAI,CAAC,oBAAoB,SAAS,EAAE;oBACzD,oBAAoB,iBAAiB,CAAC;wBACpC,wBAAwB,gBAAgB,EAAE;wBAC1C,iBAAiB;oBACnB;gBACF;YACF;QACF;kCAAG;QACD;QACA,4BAAA,sCAAA,gBAAiB,EAAE;QACnB,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM;QACxB,wDAAwD;QACxD,oBAAoB,SAAS;QAC7B,oBAAoB,iBAAiB;QACrC;QACA;KACD;IAED,oBAAoB;IACpB,mDAAmD;IACnD,gBAAgB;IAChB,MAAM;IACN,uDAAuD;IAEvD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,6EAA6E;IAE7E,MAAM,gBAAgB;QACpB,IACE,OAAO,OAAO,CACZ,uFACE,+GACA,uEAEJ;YACA,IAAI,CAAA,qBAAA,+BAAA,SAAU,EAAE,MAAI,4BAAA,sCAAA,gBAAiB,EAAE,MAAI,kCAAA,4CAAA,sBAAuB,EAAE,GAAE;gBACpE,4CAA4C;gBAC5C,IAAI,EAAC,iBAAA,2BAAA,KAAM,aAAa,GAAE;oBACxB,IAAI,oBAAoB,SAAS,EAAE;wBACjC,MACE;wBAEF;oBACF,OAAO;wBACL,2BAA2B;wBAC3B,oBAAoB,iBAAiB,CAAC;4BACpC,wBAAwB,gBAAgB,EAAE;4BAC1C,iBAAiB;wBACnB;wBACA,MAAM;wBACN;oBACF;gBACF;gBAEA,sDAAsD;gBACtD,MAAM,oBAAoB,IAAI,IAC5B,MAAM,OAAO,CAAC,iBAAA,2BAAA,KAAM,UAAU,IAC1B,KAAK,UAAU,CACZ,MAAM,CAAC,CAAC,KAAO,CAAA,eAAA,yBAAA,GAAI,WAAW,MAAK,MACnC,GAAG,CAAC,CAAC,KAAO,OAAO,GAAG,EAAE,KAC3B,EAAE;gBAGR,MAAM,uBAAiC,MAAM,IAAI,CAC/C,mBACA,GAAG,CAAC,CAAC,KAAO,OAAO;gBAErB,YAAY,MAAM,CAChB;oBACE,SAAS;oBACT,iBAAiB;oBACjB,kBAAkB,gBAAgB,EAAE;oBACpC,gBAAgB,sBAAsB,EAAE;oBACxC,wBACE,wBAAwB,qBAAqB,MAAM,GAAG,IAClD,uBACA;gBACR,GACA;oBACE,WAAW,CAAC;wBACV,OAAO,IAAI,CAAC,AAAC,mBAA0B,OAAR,KAAK,EAAE;oBACxC;gBACF;YAEJ;QACF;IACF;IAEA,qBACE;kBACG,CAAC,uBACA,6LAAC,uKAAS;YAAC,SAAQ;;;;;uDACjB,0BACF,6LAAC,+JAAO;YAAC,SAAS;YAAM,MAAM;YAAI,OAAM;YAAU,aAAY;;;;;uDAC5D,sBACF,6LAAC,2JAAK;YAAC,SAAQ;YAAQ,SAAS,MAAM,OAAO;;;;;uDAC3C,CAAC,QACH,CAAA,iBAAA,4BAAA,oBAAA,KAAM,UAAU,cAAhB,wCAAA,kBAAkB,MAAM,MAAK,KAC7B,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,kBAC7B,6LAAC;YAAI,WAAW,uNAAM,CAAC,UAAU;;8BAC/B,6LAAC;8BAAE;;;;;;8BACH,6LAAC,0KAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;qEAGjB;sBACE,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,uNAAM,CAAC,WAAW;kCAAE;;;;;;oBAElC,oBAAoB,SAAS,kBAC5B,6LAAC,2JAAK;wBAAC,SAAQ;wBAAO,SAAQ;;;;;;oBAG/B,CAAA,iBAAA,2BAAA,KAAM,aAAa,mBAClB,6LAAC,2JAAK;wBACJ,SAAQ;wBACR,SAAS,AAAC,8DAMT,OALC,EAAA,wBAAA,KAAK,eAAe,cAApB,4CAAA,sBAAsB,aAAa,IAC/B,IAAI,KACF,KAAK,eAAe,CAAC,aAAa,EAClC,kBAAkB,KACpB;;;;;;kCAKV,6LAAC;wBAAI,WAAW,uNAAM,CAAC,qBAAqB;;0CAC1C,6LAAC;;kDACC,6LAAC;wCAAQ,WAAW,uNAAM,CAAC,YAAY;;0DACrC,6LAAC;gDAAI,WAAW,uNAAM,CAAC,eAAe;;kEACpC,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;;4DAAE;4DACY,qBAAA,+BAAA,SAAU,UAAU;4DAAC;4DAAE,qBAAA,+BAAA,SAAU,SAAS;;;;;;;kEAEzD,6LAAC;;4DAAE;4DAAQ,qBAAA,+BAAA,SAAU,YAAY;;;;;;;kEACjC,6LAAC;;4DAAE;4DAAW,qBAAA,+BAAA,SAAU,KAAK;;;;;;;;;;;;;0DAE/B,6LAAC;gDAAI,WAAW,uNAAM,CAAC,gBAAgB;;kEACrC,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;;4DACE,4BAAA,sCAAA,gBAAiB,SAAS;4DAAC;0EAAC,6LAAC;;;;;4DAC7B,4BAAA,sCAAA,gBAAiB,WAAW;4DAAC;0EAAC,6LAAC;;;;;4DAC/B,4BAAA,sCAAA,gBAAiB,WAAW;4DAAC;0EAAC,6LAAC;;;;;4DAC/B,4BAAA,sCAAA,gBAAiB,eAAe;0EACjC,6LAAC;;;;;;;;;;;;;;;;;;;;;;;kDAIP,6LAAC;;;;;kDACD,6LAAC;wCAAI,WAAW,uNAAM,CAAC,IAAI;kDACzB,cAAA,6LAAC,gMAAa;4CAAC,WAAW,KAAK,UAAU;;;;;;;;;;;kDAE3C,6LAAC;;;;;oCAEA,oBAAoB,KAAK,kBACxB,6LAAC;;0DACC,6LAAC,2JAAK;gDACJ,SAAQ;gDACR,SAAS,AAAC,gCAAiE,OAAlC,oBAAoB,KAAK,CAAC,OAAO;;;;;;0DAE5E,6LAAC;gDACC,WAAW,AAAC,GAA6B,OAA3B,uNAAM,CAAC,mBAAmB,EAAC;gDACzC,SAAS,IACP,CAAA,4BAAA,sCAAA,gBAAiB,EAAE,KACnB,oBAAoB,iBAAiB,CAAC;wDACpC,wBAAwB,gBAAgB,EAAE;wDAC1C,iBAAiB;oDACnB;0DAWH;;;;;;;;;;;;kDAML,6LAAC;kDACC,cAAA,6LAAC;4CAAI,WAAW,uNAAM,CAAC,eAAe;;8DACpC,6LAAC;8DAAG;;;;;;gDACH,CAAA,uBAAA,iCAAA,WAAY,SAAS,kBACpB,6LAAC,2JAAK;oDACJ,SAAQ;oDACR,SAAQ;;;;;+FAER,CAAA,uBAAA,iCAAA,WAAY,KAAK,kBACnB,6LAAC,2JAAK;oDACJ,SAAQ;oDACR,SAAS,WAAW,KAAK,CAAC,OAAO;;;;;+FAEjC,CAAA,uBAAA,kCAAA,mBAAA,WAAY,IAAI,cAAhB,uCAAA,iBAAkB,MAAM,MAAK,kBAC/B,6LAAC,2JAAK;oDACJ,SAAQ;oDACR,SAAQ;;;;;6GAGV;8DACG,uBAAA,kCAAA,oBAAA,WAAY,IAAI,cAAhB,wCAAA,kBAAkB,GAAG,CACpB,CAAC,uBACC,6LAAC;;8EACC,6LAAC;oEACC,MAAK;oEACL,IAAI,AAAC,WAAoB,OAAV,OAAO,EAAE;oEACxB,MAAK;oEACL,SACE,CAAA,kCAAA,4CAAA,sBAAuB,EAAE,MAAK,OAAO,EAAE;oEAEzC,UAAU,IACR,0BAA0B;;;;;;8EAG9B,6LAAC;oEAAM,SAAS,AAAC,WAAoB,OAAV,OAAO,EAAE;8EACjC,OAAO,IAAI;;;;;;;2DAbN,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAuBjC,6LAAC;gCAAQ,WAAW,uNAAM,CAAC,aAAa;;kDACtC,6LAAC,sMAAmB;wCAClB,WAAW,KAAK,UAAU;wCAC1B,UAAU,EAAE,iBAAA,2BAAA,KAAM,WAAW;wCAC7B,YAAY,EAAE,iBAAA,2BAAA,KAAM,aAAa;wCACjC,WAAW,EAAE,iBAAA,2BAAA,KAAM,YAAY;wCAC/B,UAAU,EAAE,iBAAA,2BAAA,KAAM,WAAW;wCAC7B,UAAU,EAAE,iBAAA,2BAAA,KAAM,UAAU;wCAC5B,WAAW,EAAE,iBAAA,2BAAA,KAAM,WAAW;wCAC9B,sBAAsB,CAAC,EAAC,iBAAA,2BAAA,KAAM,aAAa;;;;;;kDAE7C,6LAAC;wCACC,MAAK;wCACL,UACE,YAAY,SAAS,IAAI,oBAAoB,SAAS;wCAExD,SAAS;kDAET,cAAA,6LAAC,2KAAW;4CACV,WACE,YAAY,SAAS,IAAI,oBAAoB,SAAS;4CAExD,aACE,oBAAoB,SAAS,GACzB,iCACA;4CAEN,YAAW;4CACX,aAAa;4CACb,cAAa;4CACb,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhC;GA7TM;;QACW,kJAAS;QAEG,0JAAkB;QAOV,2IAAO;QAElB,mJAAc;QACnB,yJAAiB;QACR,0JAAsB;;;KAd9C;uCA+TS", "debugId": null}}]}