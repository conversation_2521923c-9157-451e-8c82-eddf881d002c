{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(auth)/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_animations.scss", "turbopack:///turbopack:///[project]/app/(auth)/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(auth)/AuthLayout.module.scss", "turbopack:///turbopack:///[project]/app/(auth)/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/app/(auth)/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["// Define the slideIn animation globally\r\n@keyframes slideIn {\r\n  0% {\r\n    transform: translateX(-50px);\r\n    /* Start from 50px left */\r\n    opacity: 0;\r\n    /* Fully transparent */\r\n  }\r\n\r\n  100% {\r\n    transform: translateX(0);\r\n    /* Move to natural position */\r\n    opacity: 1;\r\n    /* Fully visible */\r\n  }\r\n}\r\n\r\n// Optional: Create a mixin for reusable animation settings\r\n@mixin slideInAnimation($duration: 0.5s, $easing: ease-out) {\r\n  animation: slideIn $duration $easing;\r\n}", "@use '../../src/scss/variables' as *;\n@use '../../src/scss/mixins' as *;\n@use '../../src/scss/animations' as *;\n\n.register_container {\n  background-image: url(../../public/images/pc_hardware.jpg);\n  background-color: #70829e;\n  background-size: cover;\n  background-repeat: no-repeat;\n  height: 100vh;\n  position: relative;\n  overflow: hidden; // Add this to prevent the blur from extending outside\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-image: inherit;\n    background-size: cover;\n    background-position: center;\n    filter: blur(10px);\n    z-index: 0; // Change this to 0\n  }\n\n  // Add this new rule\n  > * {\n    position: relative;\n    z-index: 1;\n  }\n}\n\n.form_container {\n  width: 400px;\n  box-shadow: #0000000d 0px 0px 0px 1px, #d1d5db 0px 0px 0px 1px inset;\n  padding: $padding-5;\n  background-color: #fff;\n  border-radius: $border-radius-1;\n  // @include slideInAnimation(0.8s, ease-out);\n  @include slideInAnimation();\n\n  button {\n    margin: 1.5rem auto 1rem auto;\n    @include btn(#fff, $primary-blue);\n    width: 100%;\n    padding: 8px 0;\n    transition: all 0.3s ease-in;\n\n    &:hover {\n      background-color: darken($primary-blue, 10%);\n    }\n  }\n}\n\n@media (width > $tablet) {\n  .register_container {\n    padding-top: 4rem;\n  }\n\n  .form_container {\n    padding: 25px;\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAGA;;;;;;;;;;AASE;;;;;;;;;;;;;;AAeA;;;;;AAMF;;;;;;;;;AASE;;;;;;;;;;;;;;AAOE;;;;AAMJ;EACE;;;;EAIA"}}]}