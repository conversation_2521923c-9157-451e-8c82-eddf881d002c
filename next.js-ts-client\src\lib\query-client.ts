// // TanStack Query client configuration for e-commerce app
// // Optimized for customer-facing application with appropriate cache settings

// import { QueryClient } from '@tanstack/react-query'

// // Create query client with e-commerce optimized defaults
// export const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       // Cache data for 5 minutes by default
//       staleTime: 5 * 60 * 1000,
//       // Keep data in cache for 10 minutes
//       gcTime: 10 * 60 * 1000,
//       // Retry configuration
//       retry: (failureCount, error: any) => {
//         // Don't retry on 4xx errors except 408 (timeout) and 429 (rate limit)
//         if (
//           error?.status >= 400 &&
//           error?.status < 500 &&
//           ![408, 429].includes(error.status)
//         ) {
//           return false
//         }
//         // Retry up to 3 times for other errors
//         return failureCount < 3
//       },
//       // Retry delay with exponential backoff
//       retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
//       // Refetch on window focus for critical data
//       refetchOnWindowFocus: false, // Disabled for better UX in e-commerce
//       // Refetch on reconnect
//       refetchOnReconnect: 'always',
//       // Network mode
//       networkMode: 'online',
//     },
//     mutations: {
//       // Don't retry mutations by default
//       retry: false,
//       // Network mode for mutations
//       networkMode: 'online',
//     },
//   },
// })

// // Entity-specific query defaults
// queryClient.setQueryDefaults(['products'], {
//   staleTime: 10 * 60 * 1000, // Products change less frequently - 10 minutes
//   gcTime: 15 * 60 * 1000, // Keep in cache longer - 15 minutes
// })

// queryClient.setQueryDefaults(['categories'], {
//   staleTime: 30 * 60 * 1000, // Categories are very stable - 30 minutes
//   gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour
// })

// queryClient.setQueryDefaults(['brands'], {
//   staleTime: 30 * 60 * 1000, // Brands are very stable - 30 minutes
//   gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour
// })

// queryClient.setQueryDefaults(['cart'], {
//   staleTime: 0, // Cart data should always be fresh
//   gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
// })

// queryClient.setQueryDefaults(['orders'], {
//   staleTime: 2 * 60 * 1000, // Orders are dynamic - 2 minutes
//   gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
// })

// queryClient.setQueryDefaults(['user'], {
//   staleTime: 5 * 60 * 1000, // User data - 5 minutes
//   gcTime: 15 * 60 * 1000, // Keep in cache for 15 minutes
// })

// queryClient.setQueryDefaults(['reviews'], {
//   staleTime: 15 * 60 * 1000, // Reviews don't change often - 15 minutes
//   gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes
// })

// queryClient.setQueryDefaults(['wishlist'], {
//   staleTime: 2 * 60 * 1000, // Wishlist can change frequently - 2 minutes
//   gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
// })

// // Global error handler for queries
// queryClient.setMutationDefaults(['auth', 'login'], {
//   mutationFn: async (variables: any) => {
//     // This will be overridden by actual mutation functions
//     throw new Error('Mutation function not implemented')
//   },
//   onError: (error: any) => {
//     console.error('Authentication error:', error)
//   },
// })

// // Query utilities for cache management
// export const queryUtils = {
//   // Invalidate all queries for an entity
//   invalidateEntity: (entity: string) => {
//     return queryClient.invalidateQueries({ queryKey: [entity] })
//   },

//   // Remove all queries for an entity
//   removeEntity: (entity: string) => {
//     return queryClient.removeQueries({ queryKey: [entity] })
//   },

//   // Get cached data for a query
//   getQueryData: <T>(queryKey: any[]) => {
//     return queryClient.getQueryData<T>(queryKey)
//   },

//   // Set cached data for a query
//   setQueryData: <T>(queryKey: any[], data: T) => {
//     return queryClient.setQueryData<T>(queryKey, data)
//   },

//   // Prefetch a query
//   prefetchQuery: (queryKey: any[], queryFn: () => Promise<any>) => {
//     return queryClient.prefetchQuery({
//       queryKey,
//       queryFn,
//     })
//   },

//   // Clear all cache
//   clear: () => {
//     return queryClient.clear()
//   },
// }

// // Cache utilities for specific operations
// export const cacheUtils = {
//   // Update product in cache after mutation
//   updateProduct: (productId: number, updater: (old: any) => any) => {
//     queryClient.setQueryData(['products', 'detail', productId], updater)
//     // Also update in product lists
//     queryClient.invalidateQueries({ queryKey: ['products', 'list'] })
//   },

//   // Update cart in cache
//   updateCart: (updater: (old: any) => any) => {
//     queryClient.setQueryData(['cart'], updater)
//   },

//   // Add item to wishlist cache
//   addToWishlist: (item: any) => {
//     queryClient.setQueryData(['wishlist'], (old: any) => {
//       if (!old) return [item]
//       return [...old, item]
//     })
//   },

//   // Remove item from wishlist cache
//   removeFromWishlist: (itemId: number) => {
//     queryClient.setQueryData(['wishlist'], (old: any[]) => {
//       if (!old) return []
//       return old.filter((item) => item.id !== itemId)
//     })
//   },

//   // Update user data in cache
//   updateUser: (updater: (old: any) => any) => {
//     queryClient.setQueryData(['user'], updater)
//   },
// }
