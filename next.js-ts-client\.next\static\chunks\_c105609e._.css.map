{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/logo/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/logo/Logo.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/logo/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/src/components/utils/logo/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\n@use '../../../scss/mixins' as *;\n\n.logo {\n  @include flexbox(flex-start, center);\n  font-weight: 700;\n  column-gap: 1px;\n\n  h1 {\n    font-size: 28px;\n    font-family: 'Poppins', sans-serif;\n    font-weight: 800;\n    color: $primary-blue;\n  }\n\n  span {\n    font-size: 23px;\n    background-color: $primary-blue;\n    padding: 0 3px 0 3px;\n    border-radius: 5px;\n    color: #fff;\n  }\n}", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAGA;;;;;;;;;AAKE;;;;;;;AAOA", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/header/navbar/navigation-card/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/navbar/navigation-card/NavigationCard.module.scss", "turbopack:///turbopack:///[project]/src/components/header/navbar/navigation-card/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/header/navbar/navigation-card/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../../scss/variables' as *;\n@use '../../../../scss/mixins' as *;\n\n.nav__card {\n  width: 100%;\n  position: absolute;\n  background-color: $sky-lighter-blue;\n  padding: $padding-2;\n  z-index: 3;\n  border-radius: 0 0 $border-radius-2 $border-radius-2;\n  box-shadow: $box-shadow-blue-1;\n\n  // Smooth appearance animation\n  animation: slideDown 0.25s ease-out;\n\n  @keyframes slideDown {\n    from {\n      opacity: 0;\n      transform: translateY(-8px);\n    }\n\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n\n  .list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n\n    li {\n      margin: calc($padding-1 / 2) 0;\n\n      a {\n        display: block;\n        padding: calc($padding-1 / 2) $padding-1;\n        border-radius: $border-radius-1;\n        font-weight: normal;\n        color: $primary-dark-text-color;\n        transition: all 0.2s ease;\n        text-decoration: none;\n\n        &:hover {\n          color: $primary-blue;\n          text-decoration: none;\n          transform: translateX(2px);\n        }\n\n        &:focus {\n          outline: 2px solid $primary-blue;\n          outline-offset: 1px;\n          background-color: rgba(255, 255, 255, 0.15);\n        }\n\n        &:focus-visible {\n          outline: 2px solid $primary-blue;\n          outline-offset: 1px;\n        }\n      }\n    }\n  }\n\n  // Alternatively, use this reusable mixin for Level List Styling\n  // @include levelStyles(5, $primary-dark, 15px);\n\n  // Level 0 styles (Horizontal layout with wrapping)\n  .level-0 {\n    @include flexbox(flex-start, flex-start, row, wrap);\n    gap: $padding-1;\n    margin-bottom: $padding-1;\n\n    li {\n      margin: 0;\n      min-width: 100px; // Ensure consistent minimum width\n\n      a {\n        font-size: $font-size-3;\n        font-weight: bold;\n        color: $primary-dark-blue;\n        padding: $padding-1 $padding-2;\n\n        &:hover {\n          transform: translateY(-1px);\n        }\n      }\n    }\n  }\n\n  // Level 1 styles (Vertical layout)\n  .level-1 {\n    margin-left: $padding-1;\n    margin-top: calc($padding-1 / 3);\n    border-left: 2px solid rgba(255, 255, 255, 0.1);\n    padding-left: calc($padding-1 / 2);\n\n    li {\n      margin: calc($padding-1 / 3) 0;\n\n      a {\n        font-size: $font-size-3;\n        font-weight: 600;\n        color: $primary-blue;\n        padding: calc($padding-1 / 3) calc($padding-1 / 2);\n      }\n    }\n  }\n\n  // Level 2 styles (Vertical layout)\n  .level-2 {\n    margin-left: $padding-1;\n    margin-top: calc($padding-1 / 4);\n\n    li {\n      margin: calc($padding-1 / 4) 0;\n\n      a {\n        font-size: $font-size-3;\n        color: $primary-dark;\n        padding: calc($padding-1 / 4) calc($padding-1 / 2);\n        font-weight: 500;\n      }\n    }\n  }\n\n  // Level 3 styles\n  .level-3 {\n    margin-left: $padding-1;\n    margin-top: calc($padding-1 / 4);\n\n    li {\n      margin: calc($padding-1 / 5) 0;\n\n      a {\n        font-size: $font-size-3;\n        font-style: italic;\n        color: $primary-lighter-text-color;\n        padding: calc($padding-1 / 5) calc($padding-1 / 2);\n        font-weight: 400;\n      }\n    }\n  }\n\n  // Responsive adjustments\n  @media (max-width: $mobile) {\n    padding: $padding-1;\n\n    .level-0 {\n      gap: $padding-1;\n\n      li {\n        min-width: 90px;\n        flex: 1 0 45%; // Two items per row on mobile\n\n        a {\n          padding: calc($padding-1 / 2) $padding-1;\n          font-size: $font-size-3;\n        }\n      }\n    }\n\n    .level-1 {\n      margin-left: $padding-1;\n      padding-left: calc($padding-1 / 2);\n    }\n\n    .level-2,\n    .level-3 {\n      margin-left: $padding-1;\n    }\n  }\n\n  @media (min-width: $mobile) {\n    .level-0 {\n      li {\n        flex: 1 0 20%; // Items take up equal space and wrap as needed\n      }\n    }\n  }\n\n  @media (min-width: 768px) {\n    .level-0 {\n      li {\n        flex: 1 0 16.66%; // Six items per row on larger screens\n      }\n    }\n  }\n}", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;;;;AAYE;;;;;;;;;;;;AAYA;;;;;;AAKE;;;;AAGE;;;;;;;;;;AASE;;;;;;AAMA;;;;;;AAMA;;;;;AAYN;;;;;;;;;AAKE;;;;;AAIE;;;;;;;AAME;;;;AAQN;;;;;;;AAME;;;;AAGE;;;;;;;AAUJ;;;;;AAIE;;;;AAGE;;;;;;;AAUJ;;;;;AAIE;;;;AAGE;;;;;;;;AAWJ;EA9IF;;;;EAiJI;;;;EAGE;;;;;EAIE;;;;;EAOJ;;;;;EAKA;;;;;AAMF;EAEI;;;;;AAMJ;EAEI", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/alert/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/alert/Alert.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/alert/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\r\n\r\n.alert {\r\n  width: fit-content;\r\n  min-width: 200px;\r\n  margin: 1rem auto 1rem auto;\r\n  padding: 10px 15px;\r\n  border-radius: 3px;\r\n\r\n  p::first-letter {\r\n    text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.info {\r\n  background-color: $info-bg;\r\n  color: $info-text;\r\n  border: 1px solid #9cceff;\r\n}\r\n\r\n.warning {\r\n  background-color: $warning-bg;\r\n  color: $warning-text;\r\n  border: 1px solid #ffd344;\r\n}\r\n\r\n.error {\r\n  background-color: $error-bg;\r\n  color: $error-text;\r\n  border: 1px solid #ff9fa7;\r\n}\r\n\r\n.success {\r\n  background-color: $success-bg;\r\n  color: $success-text;\r\n  border: 1px solid #79fc7d;\r\n}\r\n\r\n.highlight {\r\n  font-weight: bold;\r\n}", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAEA;;;;;;;;;AAOE;;;;AAKF;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/header/navbar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/navbar/Navbar.module.scss", "turbopack:///turbopack:///[project]/src/components/header/navbar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/src/components/header/navbar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../scss/mixins' as *;\n@use '../../../scss/variables' as *;\n@use 'sass:map';\n\n.navbar__links {\n  width: fit-content;\n  padding: 8px 10px;\n  font-weight: map.get($font-weight, 'medium');\n  @include flexbox(flex-start, center);\n  color: #fff;\n  height: 100%;\n  gap: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-radius: $border-radius-1;\n\n  &:hover {\n    opacity: 0.9;\n    background-color: rgba(255, 255, 255, 0.1);\n\n    i {\n      transform: translateY(1px);\n    }\n  }\n\n\n\n  button {\n    background: none;\n    border: none;\n    color: inherit;\n    font-weight: inherit;\n    font-size: inherit;\n    cursor: pointer;\n    padding: 0;\n    margin: 0;\n    @include flexbox(center, center);\n    transition: inherit;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n\n    &:focus {\n      outline: none;\n    }\n  }\n\n  i {\n    font-size: 14px;\n    @include flexbox(center, center);\n    line-height: 1;\n    transition: transform 0.2s ease;\n    height: 14px;\n    width: 14px;\n    pointer-events: none;\n  }\n}\n\n// Responsive adjustments using project media queries\n@media (max-width: $mobile) {\n  .navbar__links {\n    padding: $padding-1 $padding-2;\n    gap: calc($padding-1 / 2);\n\n    button {\n      font-size: $font-size-3;\n    }\n\n    i {\n      font-size: $font-size-1;\n      height: $font-size-1;\n      width: $font-size-1;\n    }\n  }\n}", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAIA;;;;;;;;;;;;;;;;;AAYE;;;;;AAIE;;;;AAOF;;;;;;;;;;;;;;;;;;AAcE;;;;AAKF;;;;;;;;;;;;;AAYF;EACE;;;;;EAIE;;;;EAIA", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/header/search-bar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/search-bar/Search.module.scss", "turbopack:///turbopack:///[project]/src/components/header/search-bar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/src/components/header/search-bar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\r\n@use '../../../scss/mixins' as *;\r\n@use 'sass:color';\r\n@use 'sass:map';\r\n\r\n.search {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  form {\r\n    @include flexbox(flex-start, stretch);\r\n    width: 100%;\r\n\r\n    input {\r\n      flex-grow: 1;\r\n      padding: 0.2rem 0.3rem;\r\n      border: 1px solid #ccc;\r\n      border-radius: 4px 0 0 0;\r\n      font-size: 1rem;\r\n\r\n      &:focus {\r\n        outline: none;\r\n      }\r\n\r\n      &::selection {\r\n        background-color: $sky-lighter-blue;\r\n        color: $primary-dark-blue;\r\n      }\r\n    }\r\n\r\n    button {\r\n      @include btn(#fff, $primary-dark-blue);\r\n      border: none;\r\n      border-radius: 0 4px 4px 0;\r\n      padding: 0.6rem;\r\n      cursor: pointer;\r\n      @include flexbox(center, center);\r\n\r\n      i {\r\n        font-size: 1.3rem;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search_suggestions {\r\n    position: absolute;\r\n    // top: 100%;\r\n    // left: 0;\r\n    width: 100%;\r\n    z-index: 5;\r\n\r\n    .backdrop {\r\n      position: fixed;\r\n      // top: 0;\r\n      left: 0;\r\n      width: 100vw;\r\n      height: 100vh;\r\n      background-color: rgba(0, 0, 0, 0.4);\r\n      z-index: 4;\r\n    }\r\n\r\n    .suggestions {\r\n      position: relative;\r\n      background-color: #fff;\r\n      border: 1px solid #ccc;\r\n      border-top: none;\r\n      border-radius: 0 0 4px 4px;\r\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n      z-index: 6;\r\n      max-height: 300px;\r\n      overflow-y: auto;\r\n      // padding: 0.5rem;\r\n    }\r\n\r\n    .category_item {\r\n      position: relative;\r\n      border-bottom: 1px solid color.adjust($sky-light-blue, $lightness: 10%);\r\n      transition: background-color 0.2s ease;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &:hover {\r\n        background-color: color.adjust($sky-lighter-blue, $lightness: 8%);\r\n      }\r\n\r\n      &.focused {\r\n        background-color: $sky-lighter-blue;\r\n        outline: 2px solid $primary-blue;\r\n        outline-offset: -2px;\r\n      }\r\n\r\n      &.expandable {\r\n        .category_header {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .category_header {\r\n        @include flexbox(flex-start, center);\r\n        padding: $padding-1 $padding-2;\r\n        min-height: 40px;\r\n\r\n        .expand_toggle {\r\n          @include flexbox(center, center);\r\n          width: 24px;\r\n          height: 24px;\r\n          margin-right: $padding-1;\r\n          background: none;\r\n          border: none;\r\n          cursor: pointer;\r\n          border-radius: $border-radius-1;\r\n          color: $primary-lighter-text-color;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background-color: $sky-light-blue;\r\n            color: $primary-dark-blue;\r\n          }\r\n\r\n          &:focus {\r\n            outline: 2px solid $primary-blue;\r\n            outline-offset: 2px;\r\n          }\r\n\r\n          svg {\r\n            font-size: 14px;\r\n            transition: transform 0.2s ease;\r\n          }\r\n\r\n          &.expanded svg {\r\n            transform: rotate(90deg);\r\n          }\r\n        }\r\n\r\n        .category_link {\r\n          @include flexbox(space-between, center);\r\n          flex: 1;\r\n          padding: $padding-1 $padding-2;\r\n          color: $primary-dark-text-color !important; // Force text visibility\r\n          text-decoration: none;\r\n          border-radius: $border-radius-1;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background-color: $sky-light-blue;\r\n            color: $primary-dark-blue !important; // Force hover text visibility\r\n            text-decoration: underline; // Add underline on hover\r\n          }\r\n\r\n          &:focus {\r\n            outline: 2px solid $primary-blue;\r\n            outline-offset: 2px;\r\n          }\r\n\r\n          &.parent_category {\r\n            .category_title {\r\n              font-weight: map.get($font-weight, 'medium');\r\n            }\r\n          }\r\n\r\n          &.leaf_category {\r\n            .category_title {\r\n              font-weight: map.get($font-weight, 'regular');\r\n            }\r\n          }\r\n\r\n          .category_title {\r\n            font-size: $font-size-3;\r\n            color: inherit; // Inherit color from parent link\r\n\r\n            // Different styles for hierarchy levels\r\n            &.level_0 {\r\n              font-weight: map.get($font-weight, 'medium');\r\n            }\r\n\r\n            &.level_1 {\r\n              font-weight: map.get($font-weight, 'regular');\r\n            }\r\n\r\n            &.level_2 {\r\n              font-weight: map.get($font-weight, 'regular');\r\n              font-size: $font-size-1;\r\n            }\r\n\r\n            &.level_3 {\r\n              font-weight: map.get($font-weight, 'light');\r\n              font-size: $font-size-1;\r\n            }\r\n          }\r\n\r\n          .product_count {\r\n            font-size: $font-size-1;\r\n            color: $primary-lighter-text-color;\r\n            font-weight: map.get($font-weight, 'regular');\r\n            margin-left: $padding-1;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tree-like structure for child categories with navigation lines\r\n      .child_categories {\r\n        position: relative;\r\n        background-color: color.adjust($sky-lighter-blue, $lightness: 5%);\r\n        margin-left: 12px;\r\n\r\n        // Add vertical navigation line\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          left: -8px;\r\n          top: 0;\r\n          bottom: 0;\r\n          width: 2px;\r\n          background-color: $sky-light-blue;\r\n        }\r\n\r\n        .category_item {\r\n          position: relative;\r\n          border-bottom: 1px solid\r\n            color.adjust($sky-light-blue, $lightness: 15%);\r\n\r\n          // Add horizontal navigation line for each child\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            left: -8px;\r\n            top: 20px;\r\n            width: 6px;\r\n            height: 2px;\r\n            background-color: $sky-light-blue;\r\n          }\r\n\r\n          &:hover {\r\n            background-color: color.adjust($sky-lighter-blue, $lightness: 3%);\r\n          }\r\n\r\n          // Enhanced hover effect for child category links\r\n          .category_header .category_link {\r\n            color: $primary-dark-text-color !important; // Ensure child text is visible\r\n\r\n            &:hover {\r\n              text-decoration: underline;\r\n              background-color: $sky-light-blue;\r\n              color: $primary-dark-blue !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .no_suggestions {\r\n      display: block;\r\n      padding: $padding-3;\r\n      color: $primary-lighter-text-color;\r\n      font-style: italic;\r\n      text-align: center;\r\n    }\r\n\r\n    // Mobile responsive styles\r\n    @include mobile {\r\n      .category_item {\r\n        .category_header {\r\n          padding: $padding-1 $padding-2;\r\n          min-height: 44px; // Touch-friendly minimum\r\n\r\n          // Enhanced touch interactions\r\n          &:active {\r\n            background-color: $sky-light-blue;\r\n            transform: scale(0.98);\r\n            transition: all 0.1s ease;\r\n          }\r\n\r\n          .expand_toggle {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-right: $padding-2;\r\n\r\n            // Better touch target\r\n            &:active {\r\n              background-color: $primary-blue;\r\n              color: white;\r\n              transform: scale(0.95);\r\n            }\r\n\r\n            svg {\r\n              font-size: 16px;\r\n            }\r\n          }\r\n\r\n          .category_link {\r\n            padding: $padding-1;\r\n            min-height: 44px;\r\n            @include flexbox(space-between, center);\r\n\r\n            // Touch feedback with underline\r\n            &:active {\r\n              background-color: $sky-light-blue;\r\n              transform: scale(0.98);\r\n              text-decoration: underline;\r\n            }\r\n\r\n            &:hover {\r\n              text-decoration: underline; // Ensure underline on mobile hover/touch\r\n            }\r\n\r\n            .category_title {\r\n              font-size: $font-size-3;\r\n              line-height: 1.4;\r\n\r\n              &.level_2,\r\n              &.level_3 {\r\n                font-size: $font-size-3;\r\n              }\r\n            }\r\n\r\n            .product_count {\r\n              font-size: $font-size-3;\r\n            }\r\n          }\r\n        }\r\n\r\n        .child_categories {\r\n          margin-left: 12px;\r\n\r\n          // Adjust navigation lines for mobile\r\n          &::before {\r\n            left: -6px;\r\n          }\r\n\r\n          .category_item {\r\n            &::before {\r\n              left: -6px;\r\n              width: 4px;\r\n            }\r\n\r\n            // Smoother animations on mobile\r\n            animation: slideIn 0.2s ease-out;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Improved scrolling on mobile\r\n      .suggestions {\r\n        max-height: 60vh;\r\n        -webkit-overflow-scrolling: touch;\r\n        scroll-behavior: smooth;\r\n      }\r\n    }\r\n\r\n    // Animation for mobile category expansion\r\n    @keyframes slideIn {\r\n      from {\r\n        opacity: 0;\r\n        transform: translateY(-10px);\r\n      }\r\n\r\n      to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAKA;;;;;AAIE;;;;;;;;AAIE;;;;;;;;AAOE;;;;AAIA;;;;;AAMF;;;;;;;;;;;;;AAQE;;;;AAMJ;;;;;;AAOE;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;AAKE;;;;AAIA;;;;AAIA;;;;;;AAOE;;;;AAKF;;;;;;;;;AAKE;;;;;;;;;;;;;;;;AAYE;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAKF;;;;;;;;;;;;;AASE;;;;;;AAMA;;;;;AAME;;;;AAMA;;;;AAKF;;;;;AAKE;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAMF;;;;;;;AAUJ;;;;;;AAME;;;;;;;;;;AAUA;;;;;AAME;;;;;;;;;;AAUA;;;;AAKA;;;;AAGE;;;;;;AAUR;;;;;;;;AAxNF;EAmOM;;;;;EAKE;;;;;;EAMA;;;;;;EAME;;;;;;EAMA;;;;EAKF;;;;;;;;;EAME;;;;;;EAMA;;;;EAIA;;;;;EAIE;;;;EAYN;;;;EAIE;;;;EAIA;;;;EACE;;;;;EAYN;;;;;;;AAQF", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/header/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/Header.module.scss", "turbopack:///turbopack:///[project]/src/components/header/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/header/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../scss/variables' as *;\n@use '../../scss/mixins' as *;\n@use 'sass:color';\n\n.header {\n  background-color: $primary-dark;\n}\n\n.header__top {\n  display: grid;\n  grid-template-columns: repeat(3, auto);\n  color: #fff;\n  gap: 1rem;\n  padding: 10px;\n  align-items: center;\n}\n\n.header__search {\n  grid-column: 2 / 4;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.header__end {\n  // background-color: rgb(149, 230, 230);\n  @include flexbox(flex-end, center, row);\n\n  grid-column: 1 / 4;\n  column-gap: 1rem;\n  white-space: nowrap;\n  padding: 0 5px;\n}\n\n.cart {\n  @include flexbox(flex-start, center);\n\n  p {\n    position: absolute;\n    margin: -30px 0 0 10px;\n  }\n\n  a {\n    margin: 0 0 0 5px;\n    color: #fff;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n\n.header__sign_in {\n  position: relative;\n\n  .header__login {\n    @include flexbox(flex-start, center);\n    cursor: pointer;\n    // padding: 6px 12px;\n    border-radius: $border-radius-1;\n    transition: all 0.2s ease;\n    color: #fff;\n\n    &:hover {\n      background-color: rgba(255, 255, 255, 0.1);\n      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);\n    }\n\n    >* {\n      color: inherit;\n      transition: all 0.2s ease;\n    }\n\n    i {\n      font-size: $font-size-3;\n      margin-left: 6px;\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      line-height: 1;\n      transition: transform 0.2s ease;\n    }\n\n    &:hover i {\n      transform: translateY(1px);\n    }\n\n    /* Normalize inner text/links alignment */\n    p {\n      @include flexbox(flex-start, center);\n      gap: 6px;\n      margin: 0;\n      line-height: 1;\n    }\n  }\n\n  /* Modifier: when showing only Sign In / Sign Up links (logged out),\n     do not make the whole area look/behave like a button */\n  .header__login_links {\n    cursor: default;\n\n    &:hover {\n      background-color: transparent;\n      box-shadow: none;\n    }\n\n    p {\n      @include flexbox(flex-start, center);\n      gap: 6px;\n      margin: 0;\n      line-height: 1;\n\n      a {\n        text-decoration: none;\n        margin: 0 2px;\n        color: #fff;\n        position: relative;\n        display: inline-block;\n\n        /* hover decoration handled by animated underline */\n\n        /* Animated underline */\n        &::after {\n          content: '';\n          position: absolute;\n          left: 0;\n          bottom: -2px;\n          height: 1px;\n          width: 100%;\n          background-color: currentColor;\n          transform: scaleX(0);\n          transform-origin: left center;\n          transition: transform 60ms ease-in-out;\n        }\n\n        &:hover::after {\n          transform: scaleX(1);\n        }\n      }\n    }\n  }\n\n  .warning_link {\n    padding: 0 0 0 0;\n    color: $warning-bg;\n\n    &:hover {\n      background: none;\n      text-decoration: underline;\n    }\n  }\n\n  .dropdown_container {\n    position: absolute;\n    right: -13px;\n    top: calc(100% + 5px);\n    width: 260px;\n    z-index: 1000;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(8px);\n    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n\n    /* Hover bridge to prevent disappearing */\n    &::after {\n      content: '';\n      position: absolute;\n      top: -10px;\n      left: 0;\n      right: 0;\n      height: 10px;\n      background: transparent;\n    }\n\n    &.active {\n      opacity: 1;\n      visibility: visible;\n      transform: translateY(0);\n    }\n\n    .dropdown {\n      background: #fff;\n      border-radius: $border-radius-2;\n      border: 1px solid #e5e7eb;\n      overflow: hidden;\n\n      .dropdown_header {\n        padding: $padding-3 $padding-4;\n        border-bottom: 1px solid #e5e7eb;\n        background-color: #f9fafb;\n\n        h4 {\n          color: $primary-dark-blue;\n          margin: 0;\n          font-size: $font-size-3;\n          font-weight: 600;\n          text-align: left !important;\n          line-height: 1.4;\n        }\n      }\n\n      .dropdown_menu {\n        padding: $padding-1 0;\n\n        .menu_item {\n          @include flexbox(flex-start, center);\n          padding: 10px $padding-4;\n          color: $primary-dark-text-color;\n          font-size: $font-size-3;\n          font-weight: 500;\n          transition: all 0.15s ease;\n          cursor: pointer;\n\n          &:hover {\n            background-color: $sky-lighter-blue;\n            color: $primary-blue;\n\n            i {\n              color: $primary-blue;\n            }\n          }\n\n          i {\n            margin-right: $padding-3;\n            font-size: $font-size-4;\n            color: $primary-lighter-text-color;\n            width: 24px;\n            text-align: center;\n            transition: color 0.15s ease;\n          }\n\n          a {\n            color: inherit;\n            text-decoration: none;\n            width: 100%;\n            display: flex;\n            align-items: center;\n          }\n        }\n\n        .divider {\n          height: 1px;\n          background-color: #e5e7eb;\n          margin: $padding-1 0;\n        }\n      }\n\n      .dropdown_footer {\n        // padding: $padding-2 $padding-4;\n        border-top: 1px solid #e5e7eb;\n        background-color: #f9fafb;\n\n        .menu_item {\n          @include flexbox(center, center);\n          color: $error-red;\n          font-weight: 600;\n          padding: 8px 0;\n          border-radius: $border-radius-1;\n          transition: all 0.15s ease;\n          gap: $padding-2;\n\n          &:hover {\n            background-color: #fee2e2;\n            color: color.adjust($error-red, $lightness: -10%);\n            cursor: pointer;\n          }\n\n          i {\n            color: inherit;\n            display: flex;\n            align-items: center;\n          }\n        }\n      }\n    }\n  }\n}\n\n.header__bottom_nav {\n  background-color: $primary-dark-blue;\n}\n\n.wishlist {\n  @include flexbox(flex-start, center);\n\n  p {\n    position: absolute;\n    margin: -30px 0 0 10px;\n  }\n\n  a {\n    margin: 0 0 0 5px;\n    color: #fff;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n\n.header__icon {\n  font-size: 20px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n}\n\n.header__badge {\n  @include flexbox(center, center);\n  border-radius: 50%;\n  background-color: $primary-blue;\n  width: 22px;\n  height: 22px;\n\n  span {\n    font-size: 15px;\n  }\n}\n\n@media (width > $tablet) {\n  .header {\n    padding: 6px 0;\n  }\n\n  .header__top {\n    grid-template-columns: 200px minmax(400px, 3fr) 300px;\n    width: 100%;\n    margin: 0 auto;\n    padding: 10px 0;\n    gap: 2rem;\n  }\n\n  .header__logo {\n    grid-column: 1 / 2;\n  }\n\n  .header__search {\n    grid-column: 2 / 3;\n  }\n\n  .header__end {\n    grid-column: 3 / 4;\n  }\n}", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;AAUA;;;;;;;AAGE;;;;;AAKA;;;;;AAIE;;;;AAMJ;;;;AAGE;;;;;;;;;;;AAQE;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;AAUF;;;;AAGE;;;;;AAKA;;;;;;;;;;AAME;;;;;;;;AAUE;;;;;;;;;;;;;AAaA;;;;AAON;;;;;AAIE;;;;;AAMF;;;;;;;;;;;;AAYE;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;AAME;;;;;;AAKE;;;;;;;;;AAUF;;;;AAGE;;;;;;;;;;;;;AASE;;;;;AAIE;;;;AAKF;;;;;;;;;AASA;;;;;;;;AASF;;;;;;AAOF;;;;;AAKE;;;;;;;;;;;;;AASE;;;;;;AAMA;;;;;;AAWV;;;;AAIA;;;;;;;AAGE;;;;;AAKA;;;;;AAIE;;;;AAMJ;;;;;;;;AAQA;;;;;;;;;;;AAOE;;;;AAKF;EACE;;;;EAIA;;;;;;;;EAQA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/_reset.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_animations.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_common.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/app/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/globals.scss"], "sourcesContent": ["/* Modern CSS Reset */\r\n/* Box sizing rules */\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* Remove default margin and padding */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n/* Remove list styles on ul, ol elements with a list role */\r\nul[role=\"list\"],\r\nol[role=\"list\"] {\r\n  list-style: none;\r\n}\r\n\r\n/* Set core root defaults */\r\nhtml:focus-within {\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n/* Set core body defaults */\r\nbody {\r\n  min-height: 100vh;\r\n  text-rendering: optimizeSpeed;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* A elements that don't have a class get default styles */\r\na:not([class]) {\r\n  text-decoration-skip-ink: auto;\r\n}\r\n\r\n/* Make images easier to work with */\r\nimg,\r\npicture {\r\n  max-width: 100%;\r\n  display: block;\r\n}\r\n\r\n/* Inherit fonts for inputs and buttons */\r\ninput,\r\nbutton,\r\ntextarea,\r\nselect {\r\n  font: inherit;\r\n}\r\n\r\n/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */\r\n@media (prefers-reduced-motion: reduce) {\r\n  html:focus-within {\r\n    scroll-behavior: auto;\r\n  }\r\n  \r\n  *,\r\n  *::before,\r\n  *::after {\r\n    animation-duration: 0.01ms !important;\r\n    animation-iteration-count: 1 !important;\r\n    transition-duration: 0.01ms !important;\r\n    scroll-behavior: auto !important;\r\n  }\r\n}\r\n\r\n/* Additional reset for form elements */\r\nbutton {\r\n  border: none;\r\n  background: none;\r\n  cursor: pointer;\r\n}\r\n\r\ninput {\r\n  border: none;\r\n  outline: none;\r\n}\r\n\r\n/* Remove default button styles */\r\nbutton {\r\n  -webkit-appearance: none;\r\n  -moz-appearance: none;\r\n  appearance: none;\r\n}\r\n\r\n/* Remove default link styles */\r\na {\r\n  color: inherit;\r\n  text-decoration: none;\r\n}\r\n\r\n/* Ensure form elements inherit font */\r\ninput,\r\nbutton,\r\ntextarea,\r\nselect {\r\n  font-family: inherit;\r\n  font-size: inherit;\r\n}\r\n\r\n/* Remove default textarea resize */\r\ntextarea {\r\n  resize: vertical;\r\n}", "// Define the slideIn animation globally\r\n@keyframes slideIn {\r\n  0% {\r\n    transform: translateX(-50px);\r\n    /* Start from 50px left */\r\n    opacity: 0;\r\n    /* Fully transparent */\r\n  }\r\n\r\n  100% {\r\n    transform: translateX(0);\r\n    /* Move to natural position */\r\n    opacity: 1;\r\n    /* Fully visible */\r\n  }\r\n}\r\n\r\n// Optional: Create a mixin for reusable animation settings\r\n@mixin slideInAnimation($duration: 0.5s, $easing: ease-out) {\r\n  animation: slideIn $duration $easing;\r\n}", "@use './variables' as *;\r\n@use './mixins' as *;\r\n@use './animations' as *;\r\n@use 'sass:color';\r\n\r\na {\r\n  text-decoration: none;\r\n}\r\n\r\n.title {\r\n  font-size: $font-size-3;\r\n  font-weight: 600;\r\n  // margin-bottom: 1rem;\r\n  text-align: center;\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  // background-color: aqua;\r\n}\r\n\r\nhtml {\r\n  font-family: $primary-font-family;\r\n}\r\n\r\nbutton {\r\n  cursor: pointer;\r\n  border: none;\r\n  letter-spacing: 0.4px;\r\n  border-radius: 2px;\r\n  font-size: 16px;\r\n}\r\n\r\n.loading_svg {\r\n  margin: 2px 10px;\r\n  width: 20px;\r\n  height: 20px;\r\n}\r\n\r\n.logo_header {\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: $primary-dark;\r\n  padding: 10px 0;\r\n}\r\n\r\n.title {\r\n  font-size: 25px;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  margin: 1rem 0;\r\n  color: $primary-dark-blue;\r\n}\r\n\r\n.form {\r\n  .form_group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    margin: 15px 0;\r\n    row-gap: 4px;\r\n\r\n    .form_label {\r\n      font-weight: bold;\r\n      color: $primary-dark-blue;\r\n    }\r\n\r\n    .form_input {\r\n      width: 100%;\r\n      border: 0.1px solid $primary-dark-text-color;\r\n      border-radius: 3px;\r\n      padding: 5px 5px;\r\n      font-size: 16.5px;\r\n\r\n      &:focus {\r\n        outline: 2px solid $lighten-blue;\r\n        border: none;\r\n      }\r\n    }\r\n\r\n    .form_error {\r\n      color: $error-red;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.empty_btn {\r\n  @include btn($lighten-blue, #fff);\r\n  // margin: 0 auto 0 auto;\r\n  padding: 0.36rem 1.2rem;\r\n  border: 1px solid $lighten-blue;\r\n  letter-spacing: 0.7px;\r\n  transition: all 0.2s ease;\r\n\r\n  &:hover {\r\n    border: 1px solid $primary-blue;\r\n    // color: $primary-blue;\r\n  }\r\n}\r\n\r\n.delete_btn {\r\n  @include btn($error-red, #fff);\r\n  padding: 0.6rem;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    background-color: color.adjust($error-red, $lightness: 40%, $space: hsl);\r\n  }\r\n\r\n  i {\r\n    @include flexbox(center, center);\r\n  }\r\n}\r\n\r\n.success_message {\r\n  margin: 1rem 0;\r\n  // padding: 0 0 1rem 0;\r\n  text-align: center;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn_container {\r\n  // background-color: rgb(132, 189, 189);\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: center;\r\n  // align-items: center;\r\n  column-gap: 1rem;\r\n}\r\n\r\n.password__container {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  // input {\r\n  //   width: 100%;\r\n  // }\r\n\r\n  span {\r\n    position: absolute;\r\n    right: 7px;\r\n    cursor: pointer;\r\n\r\n    i {\r\n      @include flexbox(center, center);\r\n      font-size: 18px;\r\n      color: $primary-dark-text-color;\r\n    }\r\n  }\r\n}\r\n\r\n// .loading_span {\r\n//   @include flexbox(center, center);\r\n//   gap: 0.5rem;\r\n// }\r\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "@use './reset';\n@use '../src/scss/common' as *;\n@use './../src/scss/variables' as *;\n\nhtml,\nbody {\n  // max-width: 100vw;\n  // background-color: red;\n}\n\nbody {\n  // background-color: $alice-blue;\n  background-color: white;\n}"], "names": [], "mappings": "AAEA;;;;AAOA;;;;;AAMA;;;;AAMA;;;;AAKA;;;;;;AAOA;;;;;AAKA;;;;;AAOA;;;;AAQA;EACE;;;;EAIA;;;;;;;;AAWF;;;;;;AAMA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;AASA;;;;AAtGA;;;;;;;;;;;;AAIA;;;;AAIA;;;;;;AAOA;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AASE;;;;;;;AAME;;;;;AAKA;;;;;;;;AAOE;;;;;AAMF;;;;;AAOJ;;;;;;;;;;;;;;AAQE;;;;AAMF;;;;;;;;;;;;AAME;;;;AAIA;;;;;;;AAKF;;;;;;;AAQA;;;;;;;AASA;;;;;;AASE;;;;;;AAKE;;;;;;;;;AAzIJ", "debugId": null}}]}