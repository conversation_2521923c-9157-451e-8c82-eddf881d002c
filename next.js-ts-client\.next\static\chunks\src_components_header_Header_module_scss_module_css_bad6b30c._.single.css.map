{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/header/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/Header.module.scss", "turbopack:///turbopack:///[project]/src/components/header/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/header/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../scss/variables' as *;\n@use '../../scss/mixins' as *;\n@use 'sass:color';\n\n.header {\n  background-color: $primary-dark;\n}\n\n.header__top {\n  display: grid;\n  grid-template-columns: repeat(3, auto);\n  color: #fff;\n  gap: 1rem;\n  padding: 10px;\n  align-items: center;\n}\n\n.header__search {\n  grid-column: 2 / 4;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.header__end {\n  // background-color: rgb(149, 230, 230);\n  @include flexbox(flex-end, center, row);\n\n  grid-column: 1 / 4;\n  column-gap: 1rem;\n  white-space: nowrap;\n  padding: 0 5px;\n}\n\n.cart {\n  @include flexbox(flex-start, center);\n\n  p {\n    position: absolute;\n    margin: -30px 0 0 10px;\n  }\n\n  a {\n    margin: 0 0 0 5px;\n    color: #fff;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n\n.header__sign_in {\n  position: relative;\n\n  .header__login {\n    @include flexbox(flex-start, center);\n    cursor: pointer;\n    // padding: 6px 12px;\n    border-radius: $border-radius-1;\n    transition: all 0.2s ease;\n    color: #fff;\n\n    &:hover {\n      background-color: rgba(255, 255, 255, 0.1);\n      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);\n    }\n\n    >* {\n      color: inherit;\n      transition: all 0.2s ease;\n    }\n\n    i {\n      font-size: $font-size-3;\n      margin-left: 6px;\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      line-height: 1;\n      transition: transform 0.2s ease;\n    }\n\n    &:hover i {\n      transform: translateY(1px);\n    }\n\n    /* Normalize inner text/links alignment */\n    p {\n      @include flexbox(flex-start, center);\n      gap: 6px;\n      margin: 0;\n      line-height: 1;\n    }\n  }\n\n  /* Modifier: when showing only Sign In / Sign Up links (logged out),\n     do not make the whole area look/behave like a button */\n  .header__login_links {\n    cursor: default;\n\n    &:hover {\n      background-color: transparent;\n      box-shadow: none;\n    }\n\n    p {\n      @include flexbox(flex-start, center);\n      gap: 6px;\n      margin: 0;\n      line-height: 1;\n\n      a {\n        text-decoration: none;\n        margin: 0 2px;\n        color: #fff;\n        position: relative;\n        display: inline-block;\n\n        /* hover decoration handled by animated underline */\n\n        /* Animated underline */\n        &::after {\n          content: '';\n          position: absolute;\n          left: 0;\n          bottom: -2px;\n          height: 1px;\n          width: 100%;\n          background-color: currentColor;\n          transform: scaleX(0);\n          transform-origin: left center;\n          transition: transform 60ms ease-in-out;\n        }\n\n        &:hover::after {\n          transform: scaleX(1);\n        }\n      }\n    }\n  }\n\n  .warning_link {\n    padding: 0 0 0 0;\n    color: $warning-bg;\n\n    &:hover {\n      background: none;\n      text-decoration: underline;\n    }\n  }\n\n  .dropdown_container {\n    position: absolute;\n    right: -13px;\n    top: calc(100% + 5px);\n    width: 260px;\n    z-index: 1000;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(8px);\n    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n\n    /* Hover bridge to prevent disappearing */\n    &::after {\n      content: '';\n      position: absolute;\n      top: -10px;\n      left: 0;\n      right: 0;\n      height: 10px;\n      background: transparent;\n    }\n\n    &.active {\n      opacity: 1;\n      visibility: visible;\n      transform: translateY(0);\n    }\n\n    .dropdown {\n      background: #fff;\n      border-radius: $border-radius-2;\n      border: 1px solid #e5e7eb;\n      overflow: hidden;\n\n      .dropdown_header {\n        padding: $padding-3 $padding-4;\n        border-bottom: 1px solid #e5e7eb;\n        background-color: #f9fafb;\n\n        h4 {\n          color: $primary-dark-blue;\n          margin: 0;\n          font-size: $font-size-3;\n          font-weight: 600;\n          text-align: left !important;\n          line-height: 1.4;\n        }\n      }\n\n      .dropdown_menu {\n        padding: $padding-1 0;\n\n        .menu_item {\n          @include flexbox(flex-start, center);\n          padding: 10px $padding-4;\n          color: $primary-dark-text-color;\n          font-size: $font-size-3;\n          font-weight: 500;\n          transition: all 0.15s ease;\n          cursor: pointer;\n\n          &:hover {\n            background-color: $sky-lighter-blue;\n            color: $primary-blue;\n\n            i {\n              color: $primary-blue;\n            }\n          }\n\n          i {\n            margin-right: $padding-3;\n            font-size: $font-size-4;\n            color: $primary-lighter-text-color;\n            width: 24px;\n            text-align: center;\n            transition: color 0.15s ease;\n          }\n\n          a {\n            color: inherit;\n            text-decoration: none;\n            width: 100%;\n            display: flex;\n            align-items: center;\n          }\n        }\n\n        .divider {\n          height: 1px;\n          background-color: #e5e7eb;\n          margin: $padding-1 0;\n        }\n      }\n\n      .dropdown_footer {\n        // padding: $padding-2 $padding-4;\n        border-top: 1px solid #e5e7eb;\n        background-color: #f9fafb;\n\n        .menu_item {\n          @include flexbox(center, center);\n          color: $error-red;\n          font-weight: 600;\n          padding: 8px 0;\n          border-radius: $border-radius-1;\n          transition: all 0.15s ease;\n          gap: $padding-2;\n\n          &:hover {\n            background-color: #fee2e2;\n            color: color.adjust($error-red, $lightness: -10%);\n            cursor: pointer;\n          }\n\n          i {\n            color: inherit;\n            display: flex;\n            align-items: center;\n          }\n        }\n      }\n    }\n  }\n}\n\n.header__bottom_nav {\n  background-color: $primary-dark-blue;\n}\n\n.wishlist {\n  @include flexbox(flex-start, center);\n\n  p {\n    position: absolute;\n    margin: -30px 0 0 10px;\n  }\n\n  a {\n    margin: 0 0 0 5px;\n    color: #fff;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n}\n\n.header__icon {\n  font-size: 20px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n}\n\n.header__badge {\n  @include flexbox(center, center);\n  border-radius: 50%;\n  background-color: $primary-blue;\n  width: 22px;\n  height: 22px;\n\n  span {\n    font-size: 15px;\n  }\n}\n\n@media (width > $tablet) {\n  .header {\n    padding: 6px 0;\n  }\n\n  .header__top {\n    grid-template-columns: 200px minmax(400px, 3fr) 300px;\n    width: 100%;\n    margin: 0 auto;\n    padding: 10px 0;\n    gap: 2rem;\n  }\n\n  .header__logo {\n    grid-column: 1 / 2;\n  }\n\n  .header__search {\n    grid-column: 2 / 3;\n  }\n\n  .header__end {\n    grid-column: 3 / 4;\n  }\n}", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;AAUA;;;;;;;AAGE;;;;;AAKA;;;;;AAIE;;;;AAMJ;;;;AAGE;;;;;;;;;;;AAQE;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;AAUF;;;;AAGE;;;;;AAKA;;;;;;;;;;AAME;;;;;;;;AAUE;;;;;;;;;;;;;AAaA;;;;AAON;;;;;AAIE;;;;;AAMF;;;;;;;;;;;;AAYE;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;AAME;;;;;;AAKE;;;;;;;;;AAUF;;;;AAGE;;;;;;;;;;;;;AASE;;;;;AAIE;;;;AAKF;;;;;;;;;AASA;;;;;;;;AASF;;;;;;AAOF;;;;;AAKE;;;;;;;;;;;;;AASE;;;;;;AAMA;;;;;;AAWV;;;;AAIA;;;;;;;AAGE;;;;;AAKA;;;;;AAIE;;;;AAMJ;;;;;;;;AAQA;;;;;;;;;;;AAOE;;;;AAKF;EACE;;;;EAIA;;;;;;;;EAQA;;;;EAIA;;;;EAIA"}}]}