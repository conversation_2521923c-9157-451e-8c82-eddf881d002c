/* [project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss.module.css [app-client] (css) */
.SelectedCartSummary-module-scss-module__HNBufq__summary_container {
  background-color: #d4f4ff;
  border: 1px solid #d1d5db;
  border-radius: 5px;
  margin-top: 12px;
  padding: 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), inset 0 0 0 1px #d1d5db;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_header {
  border-bottom: 1px solid #d1d5db;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 10px;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_header h3 {
  color: #333;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_header .SelectedCartSummary-module-scss-module__HNBufq__item_count {
  color: #666;
  background-color: #f9fafb;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 14px;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_details {
  position: relative;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row {
  border-bottom: 1px solid rgba(209, 213, 219, .5);
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row:last-child {
  border-bottom: none;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row span {
  font-size: 16px;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row span:first-child {
  color: #666;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row .SelectedCartSummary-module-scss-module__HNBufq__price {
  color: #0091cf;
  font-size: 16px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section {
  margin-top: 12px;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row {
  border-bottom: 1px solid rgba(209, 213, 219, .5);
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  font-size: 1.2rem;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row:last-child {
  border-bottom: none;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row span:first-child {
  color: #333;
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: 2px;
  font-weight: 600;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row span:last-child {
  color: #0091cf;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__total_row {
  border-top: 1px solid #0091cf;
  margin-top: .5rem;
  padding-top: 1rem;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__total_row span {
  font-size: 1.3rem !important;
}

.SelectedCartSummary-module-scss-module__HNBufq__divider {
  background-color: #d1d5db;
  height: 1px;
  margin: 12px 0 10px;
}

.SelectedCartSummary-module-scss-module__HNBufq__checkout_section {
  flex-flow: row;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__checkout_button {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .7px;
  cursor: pointer;
  background-color: #00b3ff;
  border: none;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 1rem 0;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__checkout_button:hover {
  color: #d9d9d9;
  background-color: #008fcc;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section {
  margin-top: 12px;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_header {
  margin-bottom: 10px;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_header span {
  color: #666;
  text-transform: uppercase;
  letter-spacing: .5px;
  font-size: 14px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_row {
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  font-size: 14px;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_row .SelectedCartSummary-module-scss-module__HNBufq__savings {
  color: #2e9f1c;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__no_selection {
  text-align: center;
  color: #666;
  padding: 20px 15px;
}

.SelectedCartSummary-module-scss-module__HNBufq__no_selection p {
  margin: 0 0 5px;
  font-size: 16px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__no_selection span {
  color: #9ca3af;
  font-size: 14px;
}

.SelectedCartSummary-module-scss-module__HNBufq__loading_overlay {
  background-color: rgba(255, 255, 255, .8);
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.SelectedCartSummary-module-scss-module__HNBufq__loading_overlay span {
  color: #666;
  font-size: 14px;
  font-style: italic;
}

@media (max-width: 576px) {
  .SelectedCartSummary-module-scss-module__HNBufq__summary_container {
    margin-top: 10px;
    padding: 12px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_header h3 {
    font-size: 16px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_header .SelectedCartSummary-module-scss-module__HNBufq__item_count {
    font-size: 12px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_row {
    padding: 5px 0;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_row span {
    font-size: 14px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__comparison_row {
    font-size: 12px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row {
    padding: 5px 0;
    font-size: 1rem;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row span {
    font-size: 14px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__total_row span {
    font-size: 1.1rem !important;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__checkout_button {
    padding: 12px 0;
    font-size: 14px;
  }
}

/*# sourceMappingURL=e3841_components_cart_SelectedCartSummary_module_scss_module_css_bad6b30c._.single.css.map*/