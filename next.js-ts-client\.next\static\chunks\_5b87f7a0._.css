/* [project]/src/components/utils/empty-cart/EmptyCart.module.scss.module.css [app-client] (css) */
.EmptyCart-module-scss-module__p5kxma__empty_cart {
  text-align: center;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  max-width: 500px;
  margin: 3rem auto 0;
  padding: 20px;
  display: flex;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__icon_section {
  margin-bottom: 15px;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__icon_section svg {
  color: #666;
  font-size: 64px;
}

@media (max-width: 576px) {
  .EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__icon_section svg {
    font-size: 48px;
  }
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section {
  margin-bottom: 20px;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__heading {
  color: #333;
  margin-bottom: 12px;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
}

@media (max-width: 576px) {
  .EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__heading {
    font-size: 18px;
  }
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__description {
  color: #666;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

@media (max-width: 576px) {
  .EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__description {
    font-size: 16px;
  }
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button {
  color: #fff;
  background-color: #0091cf;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  transition: background-color .3s, transform .2s;
  display: inline-block;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button:hover {
  background-color: #00b3ff;
  text-decoration: none;
  transform: translateY(-1px);
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button:focus {
  outline-offset: 2px;
  outline: 2px solid #0091cf;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button:active {
  transform: translateY(0);
}

/* [project]/src/components/utils/underlay/Underlay.module.scss.module.css [app-client] (css) */
.Underlay-module-scss-module__PkWa8a__underlay {
  z-index: 20;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

/* [project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss.module.css [app-client] (css) */
.CartItemsList-module-scss-module__kultEW__cart_item {
  grid-template-columns: 90px 1fr;
  gap: .75rem;
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  margin: 0;
  padding: .75rem;
  display: grid;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), inset 0 0 0 1px #d1d5db;
}

.CartItemsList-module-scss-module__kultEW__cart_item__img img {
  object-fit: contain;
  width: 100%;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: .2rem;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info span {
  color: #666;
  font-size: 14px;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__title {
  font-size: 16px;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__title a {
  color: #131921;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__title:hover {
  color: #0091cf;
  text-decoration: underline;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__extra_data {
  color: #666;
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  gap: .5rem;
  font-size: 13.5px;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__extra_data p:first-child {
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity {
  flex-flow: column;
  grid-column: 1 / 3;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child {
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: .8rem;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child p {
  color: #333;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button {
  border: 1.6px solid #fff;
  border-radius: 2px;
  padding: 4px;
  transition: all .2s ease-out;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button i {
  flex-flow: row;
  justify-content: center;
  align-items: center;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:hover {
  color: #0091cf;
  border: 1.6px solid #0091cf;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:disabled:hover {
  color: inherit;
  cursor: not-allowed;
  border: 1.6px solid #fff;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5) {
  background-color: #f00000;
  border: 1.6px solid #f00000;
  transition: all .3s;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5) i {
  color: #fff;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5):hover {
  background-color: #bd0000;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5):hover i {
  color: #fff;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity p:last-child {
  color: #cf0707;
  text-transform: none;
  margin: 10px 0;
  font-weight: bold;
}

@media not (max-width: 768px) {
  .CartItemsList-module-scss-module__kultEW__cart_item {
    grid-template-columns: 110px 1fr auto;
  }

  .CartItemsList-module-scss-module__kultEW__cart_item__quantity {
    grid-column: 3 / 4;
    justify-content: center;
    width: max-content;
  }
}

/* [project]/src/components/utils/tooltip/Tooltip.module.scss.module.css [app-client] (css) */
.Tooltip-module-scss-module__vdbe-W__tooltip {
  cursor: pointer;
  position: relative;
}

.Tooltip-module-scss-module__vdbe-W__tooltip i {
  flex-flow: row;
  justify-content: center;
  align-items: center;
  display: flex;
}

.Tooltip-module-scss-module__vdbe-W__tooltip:after {
  content: attr(data-tooltip);
  color: #fff;
  opacity: 0;
  visibility: hidden;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1000;
  background-color: #333;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  transition: opacity .2s, visibility .3s;
  position: absolute;
}

.Tooltip-module-scss-module__vdbe-W__tooltip--top:after {
  bottom: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--bottom:after {
  top: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--left:after {
  top: 50%;
  right: calc(100% + 6px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--right:after {
  top: 50%;
  left: calc(100% + 6px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--hover:hover:after, .Tooltip-module-scss-module__vdbe-W__tooltip--condition:after {
  opacity: 1;
  visibility: visible;
}

.Tooltip-module-scss-module__vdbe-W__tooltip:before {
  content: "";
  opacity: 0;
  visibility: hidden;
  z-index: 1001;
  border: 4px solid rgba(0, 0, 0, 0);
  transition: opacity .2s, visibility .3s;
  position: absolute;
}

.Tooltip-module-scss-module__vdbe-W__tooltip--top:before {
  border-top-color: #333;
  bottom: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--bottom:before {
  border-bottom-color: #333;
  top: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--left:before {
  border-left-color: #333;
  top: 50%;
  right: calc(100% + 2px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--right:before {
  border-right-color: #333;
  top: 50%;
  left: calc(100% + 2px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--hover:hover:before, .Tooltip-module-scss-module__vdbe-W__tooltip--condition:before {
  opacity: 1;
  visibility: visible;
}

@media (max-width: 768px) {
  .Tooltip-module-scss-module__vdbe-W__tooltip:after {
    white-space: normal;
    word-wrap: break-word;
    max-width: 200px;
    padding: 3px 6px;
    font-size: 11px;
  }
}

/* [project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss.module.css [app-client] (css) */
.SelectedCartSummary-module-scss-module__HNBufq__summary_container {
  background-color: #d4f4ff;
  border: 1px solid #d1d5db;
  border-radius: 5px;
  margin-top: 12px;
  padding: 15px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), inset 0 0 0 1px #d1d5db;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_header {
  border-bottom: 1px solid #d1d5db;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 10px;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_header h3 {
  color: #333;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_header .SelectedCartSummary-module-scss-module__HNBufq__item_count {
  color: #666;
  background-color: #f9fafb;
  border-radius: 3px;
  padding: 5px 10px;
  font-size: 14px;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_details {
  position: relative;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row {
  border-bottom: 1px solid rgba(209, 213, 219, .5);
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row:last-child {
  border-bottom: none;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row span {
  font-size: 16px;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row span:first-child {
  color: #666;
}

.SelectedCartSummary-module-scss-module__HNBufq__summary_row .SelectedCartSummary-module-scss-module__HNBufq__price {
  color: #0091cf;
  font-size: 16px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section {
  margin-top: 12px;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row {
  border-bottom: 1px solid rgba(209, 213, 219, .5);
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px 0;
  font-size: 1.2rem;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row:last-child {
  border-bottom: none;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row span:first-child {
  color: #333;
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: 2px;
  font-weight: 600;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row span:last-child {
  color: #0091cf;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__total_row {
  border-top: 1px solid #0091cf;
  margin-top: .5rem;
  padding-top: 1rem;
}

.SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__total_row span {
  font-size: 1.3rem !important;
}

.SelectedCartSummary-module-scss-module__HNBufq__divider {
  background-color: #d1d5db;
  height: 1px;
  margin: 12px 0 10px;
}

.SelectedCartSummary-module-scss-module__HNBufq__checkout_section {
  flex-flow: row;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__checkout_button {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .7px;
  cursor: pointer;
  background-color: #00b3ff;
  border: none;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 1rem 0;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__checkout_button:hover {
  color: #d9d9d9;
  background-color: #008fcc;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section {
  margin-top: 12px;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_header {
  margin-bottom: 10px;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_header span {
  color: #666;
  text-transform: uppercase;
  letter-spacing: .5px;
  font-size: 14px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_row {
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  font-size: 14px;
  display: flex;
}

.SelectedCartSummary-module-scss-module__HNBufq__comparison_section .SelectedCartSummary-module-scss-module__HNBufq__comparison_row .SelectedCartSummary-module-scss-module__HNBufq__savings {
  color: #2e9f1c;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__no_selection {
  text-align: center;
  color: #666;
  padding: 20px 15px;
}

.SelectedCartSummary-module-scss-module__HNBufq__no_selection p {
  margin: 0 0 5px;
  font-size: 16px;
  font-weight: 600;
}

.SelectedCartSummary-module-scss-module__HNBufq__no_selection span {
  color: #9ca3af;
  font-size: 14px;
}

.SelectedCartSummary-module-scss-module__HNBufq__loading_overlay {
  background-color: rgba(255, 255, 255, .8);
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.SelectedCartSummary-module-scss-module__HNBufq__loading_overlay span {
  color: #666;
  font-size: 14px;
  font-style: italic;
}

@media (max-width: 576px) {
  .SelectedCartSummary-module-scss-module__HNBufq__summary_container {
    margin-top: 10px;
    padding: 12px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_header h3 {
    font-size: 16px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_header .SelectedCartSummary-module-scss-module__HNBufq__item_count {
    font-size: 12px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_row {
    padding: 5px 0;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__summary_row span {
    font-size: 14px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__comparison_row {
    font-size: 12px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row {
    padding: 5px 0;
    font-size: 1rem;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__summary_row span {
    font-size: 14px;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__price_summary_section .SelectedCartSummary-module-scss-module__HNBufq__total_row span {
    font-size: 1.1rem !important;
  }

  .SelectedCartSummary-module-scss-module__HNBufq__checkout_button {
    padding: 12px 0;
    font-size: 14px;
  }
}

/* [project]/app/(shop)/(checkout-process)/checkout/cart/Cart.module.scss.module.css [app-client] (css) */
.Cart-module-scss-module__EDycpa__cart h2 {
  margin: 1rem;
  font-size: 25px;
  font-weight: bold;
}

.Cart-module-scss-module__EDycpa__cart__cart_items {
  grid-template-columns: 1fr auto;
  gap: 1rem;
  margin: 1rem 0 0;
  display: grid;
}

@media (max-width: 768px) {
  .Cart-module-scss-module__EDycpa__cart__cart_items {
    grid-template-columns: 1fr;
  }
}

.Cart-module-scss-module__EDycpa__cart__summaries {
  flex-direction: column;
  gap: 1rem;
  min-width: 300px;
  display: flex;
}

@media (max-width: 768px) {
  .Cart-module-scss-module__EDycpa__cart__summaries {
    min-width: 100%;
  }
}

@media (min-width: 768px) {
  .Cart-module-scss-module__EDycpa__cart__cart_items {
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
  }
}

/*# sourceMappingURL=_5b87f7a0._.css.map*/