{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/header/search-bar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/header/search-bar/Search.module.scss", "turbopack:///turbopack:///[project]/src/components/header/search-bar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/src/components/header/search-bar/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\r\n@use '../../../scss/mixins' as *;\r\n@use 'sass:color';\r\n@use 'sass:map';\r\n\r\n.search {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  form {\r\n    @include flexbox(flex-start, stretch);\r\n    width: 100%;\r\n\r\n    input {\r\n      flex-grow: 1;\r\n      padding: 0.2rem 0.3rem;\r\n      border: 1px solid #ccc;\r\n      border-radius: 4px 0 0 0;\r\n      font-size: 1rem;\r\n\r\n      &:focus {\r\n        outline: none;\r\n      }\r\n\r\n      &::selection {\r\n        background-color: $sky-lighter-blue;\r\n        color: $primary-dark-blue;\r\n      }\r\n    }\r\n\r\n    button {\r\n      @include btn(#fff, $primary-dark-blue);\r\n      border: none;\r\n      border-radius: 0 4px 4px 0;\r\n      padding: 0.6rem;\r\n      cursor: pointer;\r\n      @include flexbox(center, center);\r\n\r\n      i {\r\n        font-size: 1.3rem;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search_suggestions {\r\n    position: absolute;\r\n    // top: 100%;\r\n    // left: 0;\r\n    width: 100%;\r\n    z-index: 5;\r\n\r\n    .backdrop {\r\n      position: fixed;\r\n      // top: 0;\r\n      left: 0;\r\n      width: 100vw;\r\n      height: 100vh;\r\n      background-color: rgba(0, 0, 0, 0.4);\r\n      z-index: 4;\r\n    }\r\n\r\n    .suggestions {\r\n      position: relative;\r\n      background-color: #fff;\r\n      border: 1px solid #ccc;\r\n      border-top: none;\r\n      border-radius: 0 0 4px 4px;\r\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n      z-index: 6;\r\n      max-height: 300px;\r\n      overflow-y: auto;\r\n      // padding: 0.5rem;\r\n    }\r\n\r\n    .category_item {\r\n      position: relative;\r\n      border-bottom: 1px solid color.adjust($sky-light-blue, $lightness: 10%);\r\n      transition: background-color 0.2s ease;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &:hover {\r\n        background-color: color.adjust($sky-lighter-blue, $lightness: 8%);\r\n      }\r\n\r\n      &.focused {\r\n        background-color: $sky-lighter-blue;\r\n        outline: 2px solid $primary-blue;\r\n        outline-offset: -2px;\r\n      }\r\n\r\n      &.expandable {\r\n        .category_header {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .category_header {\r\n        @include flexbox(flex-start, center);\r\n        padding: $padding-1 $padding-2;\r\n        min-height: 40px;\r\n\r\n        .expand_toggle {\r\n          @include flexbox(center, center);\r\n          width: 24px;\r\n          height: 24px;\r\n          margin-right: $padding-1;\r\n          background: none;\r\n          border: none;\r\n          cursor: pointer;\r\n          border-radius: $border-radius-1;\r\n          color: $primary-lighter-text-color;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background-color: $sky-light-blue;\r\n            color: $primary-dark-blue;\r\n          }\r\n\r\n          &:focus {\r\n            outline: 2px solid $primary-blue;\r\n            outline-offset: 2px;\r\n          }\r\n\r\n          svg {\r\n            font-size: 14px;\r\n            transition: transform 0.2s ease;\r\n          }\r\n\r\n          &.expanded svg {\r\n            transform: rotate(90deg);\r\n          }\r\n        }\r\n\r\n        .category_link {\r\n          @include flexbox(space-between, center);\r\n          flex: 1;\r\n          padding: $padding-1 $padding-2;\r\n          color: $primary-dark-text-color !important; // Force text visibility\r\n          text-decoration: none;\r\n          border-radius: $border-radius-1;\r\n          transition: all 0.2s ease;\r\n\r\n          &:hover {\r\n            background-color: $sky-light-blue;\r\n            color: $primary-dark-blue !important; // Force hover text visibility\r\n            text-decoration: underline; // Add underline on hover\r\n          }\r\n\r\n          &:focus {\r\n            outline: 2px solid $primary-blue;\r\n            outline-offset: 2px;\r\n          }\r\n\r\n          &.parent_category {\r\n            .category_title {\r\n              font-weight: map.get($font-weight, 'medium');\r\n            }\r\n          }\r\n\r\n          &.leaf_category {\r\n            .category_title {\r\n              font-weight: map.get($font-weight, 'regular');\r\n            }\r\n          }\r\n\r\n          .category_title {\r\n            font-size: $font-size-3;\r\n            color: inherit; // Inherit color from parent link\r\n\r\n            // Different styles for hierarchy levels\r\n            &.level_0 {\r\n              font-weight: map.get($font-weight, 'medium');\r\n            }\r\n\r\n            &.level_1 {\r\n              font-weight: map.get($font-weight, 'regular');\r\n            }\r\n\r\n            &.level_2 {\r\n              font-weight: map.get($font-weight, 'regular');\r\n              font-size: $font-size-1;\r\n            }\r\n\r\n            &.level_3 {\r\n              font-weight: map.get($font-weight, 'light');\r\n              font-size: $font-size-1;\r\n            }\r\n          }\r\n\r\n          .product_count {\r\n            font-size: $font-size-1;\r\n            color: $primary-lighter-text-color;\r\n            font-weight: map.get($font-weight, 'regular');\r\n            margin-left: $padding-1;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tree-like structure for child categories with navigation lines\r\n      .child_categories {\r\n        position: relative;\r\n        background-color: color.adjust($sky-lighter-blue, $lightness: 5%);\r\n        margin-left: 12px;\r\n\r\n        // Add vertical navigation line\r\n        &::before {\r\n          content: '';\r\n          position: absolute;\r\n          left: -8px;\r\n          top: 0;\r\n          bottom: 0;\r\n          width: 2px;\r\n          background-color: $sky-light-blue;\r\n        }\r\n\r\n        .category_item {\r\n          position: relative;\r\n          border-bottom: 1px solid\r\n            color.adjust($sky-light-blue, $lightness: 15%);\r\n\r\n          // Add horizontal navigation line for each child\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            left: -8px;\r\n            top: 20px;\r\n            width: 6px;\r\n            height: 2px;\r\n            background-color: $sky-light-blue;\r\n          }\r\n\r\n          &:hover {\r\n            background-color: color.adjust($sky-lighter-blue, $lightness: 3%);\r\n          }\r\n\r\n          // Enhanced hover effect for child category links\r\n          .category_header .category_link {\r\n            color: $primary-dark-text-color !important; // Ensure child text is visible\r\n\r\n            &:hover {\r\n              text-decoration: underline;\r\n              background-color: $sky-light-blue;\r\n              color: $primary-dark-blue !important;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .no_suggestions {\r\n      display: block;\r\n      padding: $padding-3;\r\n      color: $primary-lighter-text-color;\r\n      font-style: italic;\r\n      text-align: center;\r\n    }\r\n\r\n    // Mobile responsive styles\r\n    @include mobile {\r\n      .category_item {\r\n        .category_header {\r\n          padding: $padding-1 $padding-2;\r\n          min-height: 44px; // Touch-friendly minimum\r\n\r\n          // Enhanced touch interactions\r\n          &:active {\r\n            background-color: $sky-light-blue;\r\n            transform: scale(0.98);\r\n            transition: all 0.1s ease;\r\n          }\r\n\r\n          .expand_toggle {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-right: $padding-2;\r\n\r\n            // Better touch target\r\n            &:active {\r\n              background-color: $primary-blue;\r\n              color: white;\r\n              transform: scale(0.95);\r\n            }\r\n\r\n            svg {\r\n              font-size: 16px;\r\n            }\r\n          }\r\n\r\n          .category_link {\r\n            padding: $padding-1;\r\n            min-height: 44px;\r\n            @include flexbox(space-between, center);\r\n\r\n            // Touch feedback with underline\r\n            &:active {\r\n              background-color: $sky-light-blue;\r\n              transform: scale(0.98);\r\n              text-decoration: underline;\r\n            }\r\n\r\n            &:hover {\r\n              text-decoration: underline; // Ensure underline on mobile hover/touch\r\n            }\r\n\r\n            .category_title {\r\n              font-size: $font-size-3;\r\n              line-height: 1.4;\r\n\r\n              &.level_2,\r\n              &.level_3 {\r\n                font-size: $font-size-3;\r\n              }\r\n            }\r\n\r\n            .product_count {\r\n              font-size: $font-size-3;\r\n            }\r\n          }\r\n        }\r\n\r\n        .child_categories {\r\n          margin-left: 12px;\r\n\r\n          // Adjust navigation lines for mobile\r\n          &::before {\r\n            left: -6px;\r\n          }\r\n\r\n          .category_item {\r\n            &::before {\r\n              left: -6px;\r\n              width: 4px;\r\n            }\r\n\r\n            // Smoother animations on mobile\r\n            animation: slideIn 0.2s ease-out;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Improved scrolling on mobile\r\n      .suggestions {\r\n        max-height: 60vh;\r\n        -webkit-overflow-scrolling: touch;\r\n        scroll-behavior: smooth;\r\n      }\r\n    }\r\n\r\n    // Animation for mobile category expansion\r\n    @keyframes slideIn {\r\n      from {\r\n        opacity: 0;\r\n        transform: translateY(-10px);\r\n      }\r\n\r\n      to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAKA;;;;;AAIE;;;;;;;;AAIE;;;;;;;;AAOE;;;;AAIA;;;;;AAMF;;;;;;;;;;;;;AAQE;;;;AAMJ;;;;;;AAOE;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;AAKE;;;;AAIA;;;;AAIA;;;;;;AAOE;;;;AAKF;;;;;;;;;AAKE;;;;;;;;;;;;;;;;AAYE;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAKF;;;;;;;;;;;;;AASE;;;;;;AAMA;;;;;AAME;;;;AAMA;;;;AAKF;;;;;AAKE;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAMF;;;;;;;AAUJ;;;;;;AAME;;;;;;;;;;AAUA;;;;;AAME;;;;;;;;;;AAUA;;;;AAKA;;;;AAGE;;;;;;AAUR;;;;;;;;AAxNF;EAmOM;;;;;EAKE;;;;;;EAMA;;;;;;EAME;;;;;;EAMA;;;;EAKF;;;;;;;;;EAME;;;;;;EAMA;;;;EAIA;;;;;EAIE;;;;EAYN;;;;EAIE;;;;EAIA;;;;EACE;;;;;EAYN;;;;;;;AAQF"}}]}