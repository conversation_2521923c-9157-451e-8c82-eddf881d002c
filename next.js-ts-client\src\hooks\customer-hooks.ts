import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { CUSTOMER_DETAILS } from "../constants/constants"
import APIClient from "../lib/api-client"
import { CustomerShape } from "../types/store-types"
import { UpdateCustomerShape } from '@/app/(auth)/register/create-customer/page'


export const useCustomerDetails = (enabled: boolean = true) => {

  const apiClient = new APIClient<CustomerShape>('/customers/me/')

  return useQuery({
    queryKey: [CUSTOMER_DETAILS],
    queryFn: () => apiClient.get(),
    enabled,
    // keepPreviousData: true,
    // refetchOnWindowFocus: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
  })
}

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/customers/me/`)

  const mutation = useMutation({
    mutationFn: (customerData: Partial<UpdateCustomerShape>) => apiClient.patch(customerData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { mutation }
}

