(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "address_selection": "AddressStage-module-scss-module__cybDRG__address_selection",
  "address_stage": "AddressStage-module-scss-module__cybDRG__address_stage",
  "cart": "AddressStage-module-scss-module__cybDRG__cart",
  "contact_details": "AddressStage-module-scss-module__cybDRG__contact_details",
  "missing_addresses": "AddressStage-module-scss-module__cybDRG__missing_addresses",
});
}),
"[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "action_button": "EmptyCart-module-scss-module__p5kxma__action_button",
  "action_section": "EmptyCart-module-scss-module__p5kxma__action_section",
  "content_section": "EmptyCart-module-scss-module__p5kxma__content_section",
  "description": "EmptyCart-module-scss-module__p5kxma__description",
  "empty_cart": "EmptyCart-module-scss-module__p5kxma__empty_cart",
  "heading": "EmptyCart-module-scss-module__p5kxma__heading",
  "icon_section": "EmptyCart-module-scss-module__p5kxma__icon_section",
});
}),
"[project]/src/components/utils/empty-cart/EmptyCart.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-client] (css module)");
;
;
;
;
const EmptyCart = (param)=>{
    let { message, showIcon = true, actionText = "Go Shopping", actionHref = "/" } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].empty_cart,
        role: "region",
        "aria-label": "Empty cart",
        children: [
            showIcon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].icon_section,
                "aria-hidden": "true",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaShoppingCart"], {}, void 0, false, {
                    fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                    lineNumber: 22,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                lineNumber: 21,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].content_section,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].heading,
                        children: message ? message : "Your cart is empty"
                    }, void 0, false, {
                        fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].description,
                        children: "Add some products to your cart to get started with your shopping journey."
                    }, void 0, false, {
                        fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                        lineNumber: 30,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].action_section,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    href: actionHref,
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].action_button,
                    "aria-label": "".concat(actionText, " - Browse products to add to your cart"),
                    children: actionText
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = EmptyCart;
const __TURBOPACK__default__export__ = EmptyCart;
var _c;
__turbopack_context__.k.register(_c, "EmptyCart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/underlay/Underlay.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "underlay": "Underlay-module-scss-module__PkWa8a__underlay",
});
}),
"[project]/src/components/utils/underlay/Underlay.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/underlay/Underlay.module.scss [app-client] (css module)");
;
;
const Underlay = (param)=>{
    let { children, isOpen, onClose, bgOpacity = 0.3 } = param;
    const handleOverlayClick = (e)=>{
        if (e.target === e.currentTarget && onClose) {
            onClose();
        }
    };
    return isOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].underlay,
        style: {
            backgroundColor: "rgba(0, 0, 0, ".concat(bgOpacity, ")")
        },
        onClick: handleOverlayClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/utils/underlay/Underlay.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0)) : null;
};
_c = Underlay;
const __TURBOPACK__default__export__ = Underlay;
var _c;
__turbopack_context__.k.register(_c, "Underlay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/spinner/Spinner.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$SyncLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/SyncLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ClipLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/ClipLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$PulseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/PulseLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RiseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/RiseLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RotateLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/RotateLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ScaleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/ScaleLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$CircleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/CircleLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/underlay/Underlay.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
const Spinner = (param)=>{
    let { loading, color, size = 150, thickness = 5, bgOpacity, useUnderlay = false, spinnerType = 'sync' } = param;
    const renderSpinner = ()=>{
        const commonProps = {
            color,
            loading,
            size,
            thickness,
            'aria-label': 'Loading Spinner'
        };
        switch(spinnerType){
            case 'clip':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ClipLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 42,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'pulse':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$PulseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 44,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'rise':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RiseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 46,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'rotate':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RotateLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 48,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'scale':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ScaleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 50,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'circle':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$CircleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 52,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'sync':
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$SyncLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 55,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
        }
    };
    if (!useUnderlay) {
        return loading ? renderSpinner() : null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        isOpen: loading,
        bgOpacity: bgOpacity,
        children: renderSpinner()
    }, void 0, false, {
        fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Spinner;
const __TURBOPACK__default__export__ = Spinner;
var _c;
__turbopack_context__.k.register(_c, "Spinner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/TextLimit.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const LimitTitleLength = (param)=>{
    let { title, maxLength } = param;
    function limitTitleLength(title, maxLength) {
        if (title.length > maxLength) {
            return title.substring(0, maxLength) + '...';
        }
        return title;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: limitTitleLength(title, maxLength)
    }, void 0, false);
};
_c = LimitTitleLength;
const __TURBOPACK__default__export__ = LimitTitleLength;
var _c;
__turbopack_context__.k.register(_c, "LimitTitleLength");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/hooks/cart-selection-hooks.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useBulkCartSelection",
    ()=>useBulkCartSelection,
    "useCartItemSelection",
    ()=>useCartItemSelection,
    "useSelectedCartSummary",
    ()=>useSelectedCartSummary
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/constants.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
;
;
const useCartItemSelection = ()=>{
    _s();
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCartItemSelection.useMutation": async (param)=>{
                let { itemId, isSelected } = param;
                const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/items/").concat(itemId, "/select/"));
                return apiClient.patch({
                    is_selected: isSelected
                });
            }
        }["useCartItemSelection.useMutation"],
        // Optimistic updates: apply selection change locally before server responds
        onMutate: {
            "useCartItemSelection.useMutation": async (variables)=>{
                const { itemId, isSelected } = variables;
                if (!cartId) return {
                    previousCart: null,
                    previousSelected: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().selectedItemIds || []
                };
                // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
                await queryClient.cancelQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                        cartId
                    ]
                });
                const previousCart = queryClient.getQueryData([
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                    cartId
                ]);
                // Optimistically update the cached cart items if shape matches { cart_items: Array }
                if (previousCart && typeof previousCart === 'object') {
                    const prevObj = previousCart;
                    if (Array.isArray(prevObj.cart_items)) {
                        const prev = previousCart;
                        const newCart = {
                            ...prev,
                            cart_items: prev.cart_items.map({
                                "useCartItemSelection.useMutation": (it)=>{
                                    // runtime-safe check and shallow update
                                    if (it && typeof it === 'object') {
                                        const rec = it;
                                        if (rec.id !== undefined && Number(rec.id) === itemId) {
                                            return {
                                                ...rec,
                                                is_selected: isSelected
                                            };
                                        }
                                    }
                                    return it;
                                }
                            }["useCartItemSelection.useMutation"])
                        };
                        queryClient.setQueryData([
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                            cartId
                        ], newCart);
                    }
                }
                // Optimistically update local persisted selection array
                const currentSelection = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().selectedItemIds || [];
                const has = currentSelection.includes(itemId);
                let newSelection = currentSelection;
                if (isSelected && !has) newSelection = [
                    ...currentSelection,
                    itemId
                ];
                else if (!isSelected && has) newSelection = currentSelection.filter({
                    "useCartItemSelection.useMutation": (id)=>id !== itemId
                }["useCartItemSelection.useMutation"]);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().setSelectedItems(newSelection);
                return {
                    previousCart,
                    previousSelected: currentSelection
                };
            }
        }["useCartItemSelection.useMutation"],
        onError: {
            "useCartItemSelection.useMutation": (error, variables, context)=>{
                console.error('Error updating item selection:', error);
                // rollback cache
                if (cartId && context && typeof context === 'object') {
                    const ctx = context;
                    if (ctx.previousCart) {
                        queryClient.setQueryData([
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                            cartId
                        ], ctx.previousCart);
                    }
                    if (ctx.previousSelected) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().setSelectedItems(ctx.previousSelected);
                    }
                }
            }
        }["useCartItemSelection.useMutation"],
        onSettled: {
            "useCartItemSelection.useMutation": ()=>{
                // Ensure we refetch fresh data from the server to reconcile
                if (cartId) {
                    queryClient.invalidateQueries({
                        queryKey: [
                            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                            cartId
                        ]
                    });
                }
            }
        }["useCartItemSelection.useMutation"]
    });
};
_s(useCartItemSelection, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useBulkCartSelection = ()=>{
    _s1();
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const bulkSelectMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useBulkCartSelection.useMutation[bulkSelectMutation]": async (data)=>{
                const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/items/bulk-select/"));
                return apiClient.post(data);
            }
        }["useBulkCartSelection.useMutation[bulkSelectMutation]"],
        onSuccess: {
            "useBulkCartSelection.useMutation[bulkSelectMutation]": (data, variables)=>{
                // Update local store based on operation
                if (variables.select_all) {
                // This will be handled by the parent component that knows all item IDs
                } else if (variables.item_ids) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().setSelectedItems(variables.item_ids);
                }
                // Invalidate cart queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                        cartId
                    ]
                });
            }
        }["useBulkCartSelection.useMutation[bulkSelectMutation]"],
        onError: {
            "useBulkCartSelection.useMutation[bulkSelectMutation]": (error)=>{
                console.error('Error bulk selecting items:', error);
            }
        }["useBulkCartSelection.useMutation[bulkSelectMutation]"]
    });
    const bulkDeselectMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useBulkCartSelection.useMutation[bulkDeselectMutation]": async (data)=>{
                const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/items/bulk-deselect/"));
                return apiClient.post(data);
            }
        }["useBulkCartSelection.useMutation[bulkDeselectMutation]"],
        onSuccess: {
            "useBulkCartSelection.useMutation[bulkDeselectMutation]": (data, variables)=>{
                // Update local store based on operation
                if (variables.deselect_all) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().clearSelection();
                } else if (variables.item_ids) {
                    // Remove specific items from selection
                    const currentSelection = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().selectedItemIds || [];
                    const newSelection = currentSelection.filter({
                        "useBulkCartSelection.useMutation[bulkDeselectMutation].newSelection": (id)=>{
                            var _variables_item_ids;
                            return !((_variables_item_ids = variables.item_ids) === null || _variables_item_ids === void 0 ? void 0 : _variables_item_ids.includes(id));
                        }
                    }["useBulkCartSelection.useMutation[bulkDeselectMutation].newSelection"]);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getState().setSelectedItems(newSelection);
                }
                // Invalidate cart queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEY_CART_ITEMS"],
                        cartId
                    ]
                });
            }
        }["useBulkCartSelection.useMutation[bulkDeselectMutation]"],
        onError: {
            "useBulkCartSelection.useMutation[bulkDeselectMutation]": (error)=>{
                console.error('Error bulk deselecting items:', error);
            }
        }["useBulkCartSelection.useMutation[bulkDeselectMutation]"]
    });
    return {
        bulkSelect: bulkSelectMutation.mutate,
        bulkDeselect: bulkDeselectMutation.mutate,
        isSelectLoading: bulkSelectMutation.isPending,
        isDeselectLoading: bulkDeselectMutation.isPending,
        selectError: bulkSelectMutation.error,
        deselectError: bulkDeselectMutation.error
    };
};
_s1(useBulkCartSelection, "CAY8K9BttZ7yoVME+qTz1XorMvA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useSelectedCartSummary = ()=>{
    _s2();
    const { cartId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const fetchSummary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useSelectedCartSummary.useCallback[fetchSummary]": ()=>{
            const apiClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]("/cart/".concat(cartId, "/selected_summary/"));
            return apiClient.get();
        }
    }["useSelectedCartSummary.useCallback[fetchSummary]"], [
        cartId
    ]);
    return {
        fetchSummary
    };
} // Hook to sync frontend selection state with backend
 // export const useSyncCartSelection = () => {
 //   // avoid subscribing to the store here so the returned function identity is stable
 //   const getSelected = () => cartStore.getState().selectedItemIds || []
 //   const setSelected = (ids: number[]) =>
 //     cartStore.getState().setSelectedItems(ids)
 //   const syncWithBackend = useCallback(
 //     (cartItems: Array<{ id: number; is_selected?: boolean }>) => {
 //       // Compute backend selected ids
 //       const backendSelectedIds = cartItems
 //         .filter((item) => item.is_selected)
 //         .map((item) => item.id)
 //       // Compare lengths first for a cheap check, then confirm all ids match (order-insensitive)
 //       const current = getSelected()
 //       if (
 //         backendSelectedIds.length === current.length &&
 //         backendSelectedIds.every((id) => current.includes(id))
 //       ) {
 //         // No change -> avoid calling setSelected to prevent triggering effects
 //         return
 //       }
 //       // Only update when backend selection differs from current
 //       setSelected(backendSelectedIds)
 //     },
 //     []
 //   )
 //   return { syncWithBackend }
 // }
;
_s2(useSelectedCartSummary, "psSgZ01uaUbM/e0XokKa5zWh74w=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "cart_item": "CartItemsList-module-scss-module__kultEW__cart_item",
  "cart_item__extra_data": "CartItemsList-module-scss-module__kultEW__cart_item__extra_data",
  "cart_item__img": "CartItemsList-module-scss-module__kultEW__cart_item__img",
  "cart_item__info": "CartItemsList-module-scss-module__kultEW__cart_item__info",
  "cart_item__quantity": "CartItemsList-module-scss-module__kultEW__cart_item__quantity",
  "cart_item__title": "CartItemsList-module-scss-module__kultEW__cart_item__title",
});
}),
"[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$TextLimit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/TextLimit.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/cart-selection-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
const CartItemsList = (param)=>{
    let { cartItems, handleIncrement, handleDecrement, deleteCartItem, showSelection = true, selectedIds } = param;
    _s();
    // local persisted array (fallback when server doesn't provide selection)
    const storeSelectedItemIds = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((s)=>s.selectedItemIds);
    const toggleSelectAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((s)=>s.toggleSelectAll);
    const { mutate: updateItemSelection } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCartItemSelection"])();
    const { bulkSelect, bulkDeselect } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBulkCartSelection"])();
    // Build a local Set for O(1) lookups from persisted array
    // Prefer server-provided selection when available, otherwise use persisted array
    const safeSelectedCartItems = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "CartItemsList.useMemo[safeSelectedCartItems]": ()=>{
            if (selectedIds && selectedIds instanceof Set) return new Set(selectedIds);
            return new Set(Array.isArray(storeSelectedItemIds) ? storeSelectedItemIds : []);
        }
    }["CartItemsList.useMemo[safeSelectedCartItems]"], [
        selectedIds,
        storeSelectedItemIds
    ]);
    const handleItemSelectionChange = (itemId, currentlySelected)=>{
        // Update backend
        updateItemSelection({
            itemId,
            isSelected: !currentlySelected
        });
    };
    const handleSelectAllChange = ()=>{
        const allItemIds = cartItems.map((item)=>item.id);
        const allSelected = allItemIds.every((id)=>safeSelectedCartItems.has(id));
        if (allSelected) {
            // Deselect all
            bulkDeselect({
                deselect_all: true
            });
            // when server is source-of-truth we avoid mutating local store
            if (!selectedIds) toggleSelectAll(allItemIds);
        } else {
            // Select all
            bulkSelect({
                select_all: true,
                item_ids: allItemIds
            });
            if (!selectedIds) toggleSelectAll(allItemIds);
        }
    };
    const allItemsSelected = cartItems.length > 0 && cartItems.every((item)=>safeSelectedCartItems.has(item.id));
    const someItemsSelected = cartItems.some((item)=>safeSelectedCartItems.has(item.id));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            showSelection && cartItems.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].selection_header,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].select_all_container,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "checkbox",
                            checked: allItemsSelected,
                            ref: (input)=>{
                                if (input) input.indeterminate = someItemsSelected && !allItemsSelected;
                            },
                            onChange: handleSelectAllChange,
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].select_all_checkbox
                        }, void 0, false, {
                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                            lineNumber: 90,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                "Select All (",
                                cartItems.length,
                                " items)"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                            lineNumber: 100,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                    lineNumber: 89,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                lineNumber: 88,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                children: cartItems === null || cartItems === void 0 ? void 0 : cartItems.map((item)=>{
                    var _item_product_variant_product_image_, _item_product_variant_product_image, _item_product_variant, _item_product_variant_product_image_1, _item_product_variant_product_image1, _item_product_variant1, _item_product_variant_price_label, _item_product_variant2;
                    const isSelected = safeSelectedCartItems.has(item.id);
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                        className: "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item, " ").concat(isSelected ? __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].selected : ''),
                        children: [
                            showSelection && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__selection,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "checkbox",
                                    checked: isSelected,
                                    onChange: ()=>handleItemSelectionChange(item.id, isSelected),
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].item_checkbox
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                    lineNumber: 118,
                                    columnNumber: 19
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 117,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__img,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: ((_item_product_variant = item.product_variant) === null || _item_product_variant === void 0 ? void 0 : (_item_product_variant_product_image = _item_product_variant.product_image) === null || _item_product_variant_product_image === void 0 ? void 0 : (_item_product_variant_product_image_ = _item_product_variant_product_image[0]) === null || _item_product_variant_product_image_ === void 0 ? void 0 : _item_product_variant_product_image_.image) ? "".concat(("TURBOPACK compile-time value", "https://res.cloudinary.com/dev-kani"), "/").concat(item.product_variant.product_image[0].image) : '',
                                    alt: ((_item_product_variant1 = item.product_variant) === null || _item_product_variant1 === void 0 ? void 0 : (_item_product_variant_product_image1 = _item_product_variant1.product_image) === null || _item_product_variant_product_image1 === void 0 ? void 0 : (_item_product_variant_product_image_1 = _item_product_variant_product_image1[0]) === null || _item_product_variant_product_image_1 === void 0 ? void 0 : _item_product_variant_product_image_1.alternative_text) || item.product.title,
                                    width: 200,
                                    height: 200
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                    lineNumber: 130,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 129,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__info,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__title,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: "/product/".concat(item.product.slug),
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$TextLimit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                title: item.product.title,
                                                maxLength: 60
                                            }, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 148,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 147,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 146,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    " ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            "($",
                                            item.product_variant.price,
                                            " x ",
                                            item.quantity,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 155,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            "Variant: ",
                                            (_item_product_variant2 = item.product_variant) === null || _item_product_variant2 === void 0 ? void 0 : (_item_product_variant_price_label = _item_product_variant2.price_label) === null || _item_product_variant_price_label === void 0 ? void 0 : _item_product_variant_price_label.attribute_value
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 158,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    Object.entries(item.extra_data).map((param, index)=>{
                                        let [key, value] = param;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__extra_data,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        key,
                                                        " :"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                    lineNumber: 163,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: value
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                    lineNumber: 164,
                                                    columnNumber: 21
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 162,
                                            columnNumber: 19
                                        }, ("TURBOPACK compile-time value", void 0));
                                    })
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 145,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__quantity,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: "Qty:"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 171,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            handleDecrement && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>handleDecrement(item),
                                                disabled: item.product_variant.stock_qty === 0,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiMinus"], {}, void 0, false, {
                                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                        lineNumber: 178,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0))
                                            }, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 173,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: item.quantity
                                            }, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 182,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            handleIncrement && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>handleIncrement(item),
                                                disabled: item.product_variant.stock_qty === 0,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiPlus"], {}, void 0, false, {
                                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                        lineNumber: 189,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                    lineNumber: 188,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0))
                                            }, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 184,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            deleteCartItem && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>deleteCartItem(item.id),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrash2"], {}, void 0, false, {
                                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                        lineNumber: 196,
                                                        columnNumber: 25
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                    lineNumber: 195,
                                                    columnNumber: 23
                                                }, ("TURBOPACK compile-time value", void 0))
                                            }, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 194,
                                                columnNumber: 21
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 170,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    item.product_variant.stock_qty === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "Out of Stock"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 201,
                                        columnNumber: 58
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 169,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, item.id, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                        lineNumber: 110,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0));
                })
            }, void 0, false, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                lineNumber: 105,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(CartItemsList, "tF3Fxdofx1IIu0xAeyUZmz1HMRk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCartItemSelection"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBulkCartSelection"]
    ];
});
_c = CartItemsList;
const __TURBOPACK__default__export__ = CartItemsList;
var _c;
__turbopack_context__.k.register(_c, "CartItemsList");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "comparison_header": "SelectedCartSummary-module-scss-module__HNBufq__comparison_header",
  "comparison_row": "SelectedCartSummary-module-scss-module__HNBufq__comparison_row",
  "comparison_section": "SelectedCartSummary-module-scss-module__HNBufq__comparison_section",
  "divider": "SelectedCartSummary-module-scss-module__HNBufq__divider",
  "item_count": "SelectedCartSummary-module-scss-module__HNBufq__item_count",
  "loading_overlay": "SelectedCartSummary-module-scss-module__HNBufq__loading_overlay",
  "no_selection": "SelectedCartSummary-module-scss-module__HNBufq__no_selection",
  "price": "SelectedCartSummary-module-scss-module__HNBufq__price",
  "savings": "SelectedCartSummary-module-scss-module__HNBufq__savings",
  "summary_container": "SelectedCartSummary-module-scss-module__HNBufq__summary_container",
  "summary_details": "SelectedCartSummary-module-scss-module__HNBufq__summary_details",
  "summary_header": "SelectedCartSummary-module-scss-module__HNBufq__summary_header",
  "summary_row": "SelectedCartSummary-module-scss-module__HNBufq__summary_row",
});
}),
"[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/cart-selection-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.module.scss [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const SelectedCartSummary = (param)=>{
    let { cartItems, showComparison = false, selectedIds } = param;
    _s();
    const selectedItemIds = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((s)=>s.selectedItemIds);
    const { fetchSummary } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelectedCartSummary"])();
    const [summaryData, setSummaryData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Prefer server-provided selection when available, otherwise fall back to persisted array
    const safeSelectedCartItems = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "SelectedCartSummary.useMemo[safeSelectedCartItems]": ()=>{
            if (selectedIds && selectedIds instanceof Set) return new Set(selectedIds);
            return new Set(Array.isArray(selectedItemIds) ? selectedItemIds : []);
        }
    }["SelectedCartSummary.useMemo[safeSelectedCartItems]"], [
        selectedIds,
        selectedItemIds
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SelectedCartSummary.useEffect": ()=>{
            let mounted = true;
            const loadSummary = {
                "SelectedCartSummary.useEffect.loadSummary": async ()=>{
                    if (safeSelectedCartItems.size === 0) {
                        if (mounted) setSummaryData(null);
                        return;
                    }
                    if (mounted) setLoading(true);
                    try {
                        const data = await fetchSummary();
                        if (mounted) setSummaryData(data);
                    } catch (error) {
                        console.error('Error fetching selected cart summary:', error);
                    } finally{
                        if (mounted) setLoading(false);
                    }
                }
            }["SelectedCartSummary.useEffect.loadSummary"];
            loadSummary();
            return ({
                "SelectedCartSummary.useEffect": ()=>{
                    mounted = false;
                }
            })["SelectedCartSummary.useEffect"];
        }
    }["SelectedCartSummary.useEffect"], [
        fetchSummary,
        safeSelectedCartItems.size
    ]);
    // Calculate frontend totals for selected items
    const selectedItems = cartItems.filter((item)=>safeSelectedCartItems.has(item.id));
    const selectedCount = selectedItems.length;
    const totalCount = cartItems.length;
    const selectedSubtotal = selectedItems.reduce((sum, item)=>sum + item.qty_price, 0);
    const totalSubtotal = cartItems.reduce((sum, item)=>sum + item.qty_price, 0);
    if (selectedCount === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_container,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].no_selection,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "No items selected"
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                        lineNumber: 82,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "Select items to see checkout summary"
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                        lineNumber: 83,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                lineNumber: 81,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_container,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_header,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        children: "Selected Items Summary"
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                        lineNumber: 92,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].item_count,
                        children: [
                            selectedCount,
                            " of ",
                            totalCount,
                            " items selected"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                        lineNumber: 93,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                lineNumber: 91,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_details,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_row,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Selected Subtotal:"
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 100,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].price,
                                children: [
                                    "$",
                                    selectedSubtotal.toFixed(2)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    summaryData && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_row,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Shipping (estimated):"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 107,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].price,
                                        children: summaryData.selected_shipping_cost ? "$".concat(summaryData.selected_shipping_cost) : 'TBD'
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 108,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 106,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].summary_row,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Total Weight:"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 116,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            summaryData.selected_total_weight || 0,
                                            " kg"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 117,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true),
                    showComparison && selectedCount < totalCount && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].comparison_section,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].divider
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 124,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].comparison_header,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Comparison"
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                    lineNumber: 126,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 125,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].comparison_row,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "All Items Subtotal:"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 130,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].price,
                                        children: [
                                            "$",
                                            totalSubtotal.toFixed(2)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 131,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 129,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].comparison_row,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Savings by selecting:"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 135,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].savings,
                                        children: [
                                            "$",
                                            (totalSubtotal - selectedSubtotal).toFixed(2)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                        lineNumber: 136,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                                lineNumber: 134,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                        lineNumber: 123,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                lineNumber: 98,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            loading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loading_overlay,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: "Calculating..."
                }, void 0, false, {
                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                    lineNumber: 146,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
                lineNumber: 145,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx",
        lineNumber: 90,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(SelectedCartSummary, "9A8IR8cpX7wUv1cuctUuNGpsnKM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$selection$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSelectedCartSummary"]
    ];
});
_c = SelectedCartSummary;
const __TURBOPACK__default__export__ = SelectedCartSummary;
var _c;
__turbopack_context__.k.register(_c, "SelectedCartSummary");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/customer-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/cart-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/empty-cart/EmptyCart.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/alert/Alert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/logo/Logo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$spinner$2f$Spinner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/spinner/Spinner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/cart/SelectedCartSummary.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const AddressChoice = ()=>{
    var _customer_address, _customer_address1;
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { data: customer } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCustomerDetails"])(isLoggedIn);
    const { cartId, setSelectedAddress, selectedAddress } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { isLoading, error, data } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"])();
    const { mutate: deleteCartItem } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDeleteCartItem"])();
    const { updateCartCustomer, isPending: isUpdatingCart } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUpdateCartCustomer"])();
    const [addressesReady, setAddressesReady] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [cartUpdated, setCartUpdated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            if (!isLoggedIn) {
                router.push('/login');
            }
        }
    }["AddressChoice.useEffect"], [
        isLoggedIn,
        router
    ]);
    // Update cart with authenticated customer when page loads
    // Update cart with authenticated customer when page loads
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            if (isLoggedIn && cartId && customer && !cartUpdated && !isUpdatingCart && !(data === null || data === void 0 ? void 0 : data.customer // <-- only if cart has no customer yet
            )) {
                updateCartCustomer({});
                setCartUpdated(true);
            }
        }
    }["AddressChoice.useEffect"], [
        isLoggedIn,
        cartId,
        customer,
        cartUpdated,
        isUpdatingCart,
        updateCartCustomer,
        data === null || data === void 0 ? void 0 : data.customer
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            if ((customer === null || customer === void 0 ? void 0 : customer.address) && customer.address.length > 0) {
                if (!selectedAddress || Object.keys(selectedAddress).length === 0) {
                    setSelectedAddress(customer.address[0]);
                }
                setAddressesReady(true);
            }
        }
    }["AddressChoice.useEffect"], [
        customer,
        selectedAddress,
        setSelectedAddress
    ]);
    // Handle out-of-stock items by removing them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            var _data_cart_items;
            if (data && (data === null || data === void 0 ? void 0 : (_data_cart_items = data.cart_items) === null || _data_cart_items === void 0 ? void 0 : _data_cart_items.length) > 0) {
                const outOfStockItems = data.cart_items.filter({
                    "AddressChoice.useEffect.outOfStockItems": (item)=>item.product_variant.stock_qty === 0
                }["AddressChoice.useEffect.outOfStockItems"]);
                if (outOfStockItems.length > 0) {
                    outOfStockItems.forEach({
                        "AddressChoice.useEffect": (item)=>{
                            deleteCartItem(item.id); // Remove each out-of-stock item from the cart
                        }
                    }["AddressChoice.useEffect"]);
                }
            }
        }
    }["AddressChoice.useEffect"], [
        data,
        deleteCartItem
    ]);
    const handleAddressChange = (address)=>{
        setSelectedAddress(address);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: !cartId ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            message: "Your cart is empty. Add some products to the cart to checkout!"
        }, void 0, false, {
            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
            lineNumber: 98,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$spinner$2f$Spinner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                color: "#0091CF",
                size: 20,
                loading: true
            }, void 0, false, {
                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                lineNumber: 102,
                columnNumber: 13
            }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    variant: "error",
                    message: error.message
                }, void 0, false, {
                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                    lineNumber: 106,
                    columnNumber: 17
                }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: (customer === null || customer === void 0 ? void 0 : (_customer_address = customer.address) === null || _customer_address === void 0 ? void 0 : _customer_address.length) === 0 || (customer === null || customer === void 0 ? void 0 : customer.phone_number) === '' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "logo_header",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 113,
                                    columnNumber: 25
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                lineNumber: 112,
                                columnNumber: 23
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].missing_addresses,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        variant: "warning",
                                        textSize: "20",
                                        message: " You haven't added a shipping address yet.  Please add one along with a phone number to continue with checkout. Thank you! 😊"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                        lineNumber: 116,
                                        columnNumber: 25
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                        className: "btn_container",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: "empty_btn",
                                            onClick: ()=>router.push('/account/profile'),
                                            children: "Update Profile"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 124,
                                            columnNumber: 27
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                        lineNumber: 123,
                                        columnNumber: 25
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                lineNumber: 115,
                                columnNumber: 23
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: !data || data.cart_items.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].empty_cart,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "Your cart is empty. Add some products to the cart to checkout!"
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 137,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/",
                                    children: "Go Shopping "
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 141,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                            lineNumber: 136,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                    className: "container ".concat(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].address_stage),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            children: "Address Choices"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 149,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].contact_details,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    children: "Contact Details: "
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 151,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Deliver to: ",
                                                        customer === null || customer === void 0 ? void 0 : customer.first_name,
                                                        ' ',
                                                        customer === null || customer === void 0 ? void 0 : customer.last_name
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 152,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Phone: ",
                                                        customer === null || customer === void 0 ? void 0 : customer.phone_number
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 156,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Email to: ",
                                                        customer === null || customer === void 0 ? void 0 : customer.email
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 157,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 150,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 159,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    cartItems: data.cart_items
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 161,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$SelectedCartSummary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    cartItems: data.cart_items,
                                                    selectedIds: new Set((data.cart_items || []).filter((it)=>it === null || it === void 0 ? void 0 : it.is_selected).map((it)=>Number(it.id))),
                                                    totalPrice: data === null || data === void 0 ? void 0 : data.total_price,
                                                    item_count: data === null || data === void 0 ? void 0 : data.item_count,
                                                    cart_weight: data === null || data === void 0 ? void 0 : data.cart_weight
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 162,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 160,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 179,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        addressesReady && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].address_selection,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    children: "Choose a shipping address: "
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 183,
                                                    columnNumber: 35
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                customer === null || customer === void 0 ? void 0 : (_customer_address1 = customer.address) === null || _customer_address1 === void 0 ? void 0 : _customer_address1.map((address)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("address", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "radio",
                                                                id: "address-".concat(address.id),
                                                                name: "address",
                                                                value: address.id,
                                                                checked: (selectedAddress === null || selectedAddress === void 0 ? void 0 : selectedAddress.id) === address.id,
                                                                onChange: ()=>handleAddressChange(address)
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                                lineNumber: 186,
                                                                columnNumber: 39
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                htmlFor: "address-".concat(address.id),
                                                                children: [
                                                                    address.full_name,
                                                                    ",",
                                                                    ' ',
                                                                    address.street_name,
                                                                    ",",
                                                                    ' ',
                                                                    address.city_or_village
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                                lineNumber: 198,
                                                                columnNumber: 39
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, address.id, true, {
                                                        fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                        lineNumber: 185,
                                                        columnNumber: 37
                                                    }, ("TURBOPACK compile-time value", void 0))),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>router.push('/checkout/payment-choice'),
                                                    children: "Use this address"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 35
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 182,
                                            columnNumber: 33
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 214,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 146,
                                    columnNumber: 29
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                lineNumber: 145,
                                columnNumber: 27
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false)
                    }, void 0, false)
                }, void 0, false)
            }, void 0, false)
        }, void 0, false)
    }, void 0, false);
};
_s(AddressChoice, "Fq9PLAYTgGBK1350LYYhdfVDy8M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCustomerDetails"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDeleteCartItem"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUpdateCartCustomer"]
    ];
});
_c = AddressChoice;
const __TURBOPACK__default__export__ = AddressChoice;
var _c;
__turbopack_context__.k.register(_c, "AddressChoice");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=_b6f3c182._.js.map