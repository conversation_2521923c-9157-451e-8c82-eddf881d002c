{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/underlay/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/underlay/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\r\n@use '../../../scss/mixins' as *;\r\n\r\n.underlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  width: 100vw;\r\n  /* Set the width to 100% of the viewport width */\r\n  height: 100vh;\r\n  /* Set the height to 100% of the viewport height */\r\n  z-index: 20;\r\n  /* Set a z-index value if needed */\r\n  @include flexbox(center, center);\r\n  /* Replaced direct flexbox properties with the flexbox mixin */\r\n}", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA", "debugId": null}}, {"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/account/addresses/address-modal/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/account/addresses/address-modal/AddressModal.module.scss", "turbopack:///turbopack:///[project]/src/components/account/addresses/address-modal/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/account/addresses/address-modal/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../../scss/variables' as *;\r\n@use '../../../../scss/mixins' as *;\r\n\r\n.modal_content {\r\n  background-color: #fff;\r\n  padding: 2rem;\r\n  border-radius: $border-radius-2;\r\n  max-width: 500px;\r\n  width: 100%;\r\n\r\n  h3 {\r\n    font-size: $font-size-4;\r\n    font-weight: bold;\r\n    margin-bottom: 1rem;\r\n    text-align: center;\r\n  }\r\n\r\n  form {\r\n    // @include flexbox(flex-start, flex-start, column);\r\n\r\n    div {\r\n      margin-bottom: 1rem;\r\n\r\n      label {\r\n        font-weight: bold;\r\n        color: $primary-dark-text-color;\r\n        display: block;\r\n        margin-bottom: 0.5rem;\r\n      }\r\n\r\n      input {\r\n        width: 100%;\r\n        padding: 0.5rem;\r\n        border: 1px solid $primary-lighter-text-color;\r\n        border-radius: 3px;\r\n\r\n        &:focus {\r\n          outline: 2px solid $lighten-blue;\r\n          border: none;\r\n        }\r\n\r\n        &:read-only {\r\n          background-color: #f5f5f5;\r\n          cursor: not-allowed;\r\n        }\r\n      }\r\n\r\n      // Style for react-country-state-city selectors\r\n      :global(.react-country-state-city) {\r\n        width: 100%;\r\n        \r\n        .css-1s2u09g-control {\r\n          border: 1px solid $primary-lighter-text-color;\r\n          border-radius: 3px;\r\n          min-height: 38px;\r\n        }\r\n        \r\n        .css-1pahdxg-control {\r\n          border-color: $lighten-blue;\r\n          box-shadow: 0 0 0 1px $lighten-blue;\r\n        }\r\n      }\r\n\r\n      select {\r\n        width: 100%;\r\n        padding: 0.5rem;\r\n        border: 1px solid $primary-lighter-text-color;\r\n        border-radius: 3px;\r\n        background-color: #fff;\r\n        font-size: 1rem;\r\n\r\n        &:focus {\r\n          outline: 2px solid $lighten-blue;\r\n          border: none;\r\n        }\r\n      }\r\n\r\n      p {\r\n        color: $error-red;\r\n        font-size: 14px;\r\n        margin-top: 0.5rem;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.modal_buttons {\r\n  @include flexbox(space-between, center);\r\n  margin-top: 1rem;\r\n\r\n  button {\r\n    @include btn($primary-blue, #fff);\r\n    padding: 0.5rem 1rem;\r\n    border: 1px solid $lighten-blue;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      border: 1px solid $primary-dark-blue;\r\n      color: $primary-dark-blue;\r\n    }\r\n  }\r\n}\r\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;AAOE;;;;;;;AAUE;;;;AAGE;;;;;;;AAOA;;;;;;;AAME;;;;;AAKA;;;;;AAAA;;;;;AAOF;;;;AAGE;;;;;;AAMA;;;;;AAMF;;;;;;;;;AAQE;;;;;AAMF;;;;;;;AAUN;;;;;;;;AAIE;;;;;;;;;;;;;AAME", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/node_modules/react-country-state-city/dist/react-country-state-city.css"], "sourcesContent": ["@font-face {\n  font-family: \"Twemoji Mozilla\";\n  src: url(./fonts/TwemojiMozilla.ttf) format(\"truetype\");\n  font-weight: normal;\n  font-style: normal;\n}\n.stdropdown-container {\n  text-align: left;\n  border: 1px solid #ccc;\n  position: relative;\n  border-radius: 5px;\n  font-family: Arial, Helvetica, sans-serif;\n}\n\n.stdropdown-input input {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif, \"Twemoji Mozilla\" !important;\n}\n\n.stdropdown-flag {\n  font-family: \"Twemoji Mozilla\" !important;\n}\n\n.stdropdown-menu::-webkit-scrollbar {\n  width: 2px;\n}\n\n.stdropdown-menu::-webkit-scrollbar-track {\n  background: #f1f1f1;\n}\n\n.stdropdown-menu::-webkit-scrollbar-thumb {\n  background: #888;\n}\n\n.stdropdown-menu::-webkit-scrollbar-thumb:hover {\n  background: #555;\n}\n\n.stdropdown-input {\n  padding: 5px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  user-select: none;\n}\n\n.stdropdown-menu {\n  position: absolute;\n  transform: translateY(4px);\n  width: 100%;\n  border: 1px solid #ccc;\n  border-radius: 5px;\n  overflow: auto;\n  max-height: 150px;\n  background-color: #fff;\n  z-index: 99;\n}\n\n.stdropdown-item {\n  padding: 5px;\n  cursor: pointer;\n}\n\n.stdropdown-item:hover {\n  background-color: rgba(159, 195, 248, 0.4392156863);\n}\n\n.stdropdown-item.selected {\n  background-color: #0d6efd;\n  color: #fff;\n}\n\n.stdropdown-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 5px;\n}\n\n.stdropdown-tag-item {\n  background-color: #ddd;\n  padding: 2px 4px;\n  border-radius: 2px;\n  display: flex;\n  align-items: center;\n}\n\n.stdropdown-tag-close {\n  display: flex;\n  align-items: center;\n}\n\n.stsearch-box {\n  padding: 5px;\n  background-color: transparent;\n}\n\n.stsearch-box input {\n  width: 100%;\n  box-sizing: border-box;\n  padding: 5px;\n  border: 1px solid #ccc;\n  border-radius: 5px;\n}"], "names": [], "mappings": "AAAA;;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/account/addresses/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/account/addresses/ManageAddresses.module.scss", "turbopack:///turbopack:///[project]/src/components/account/addresses/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/account/addresses/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../../src/scss/variables' as *;\n@use '../../../../src/scss/mixins' as *;\n@use 'sass:color';\n\n.title {\n  font-size: $font-size-4;\n  font-weight: bold;\n  margin: 1rem 0;\n}\n\n.addresses {\n  width: 100%;\n\n  &__list {\n    padding: 1rem;\n    border-radius: $border-radius-3;\n    margin-bottom: 1rem;\n\n    div {\n      margin-bottom: 0.5rem;\n\n      h5 {\n        font-weight: bold;\n        color: $primary-dark-text-color;\n        margin-bottom: 0.25rem;\n        font-size: 16px;\n      }\n\n      p {\n        color: $primary-lighter-text-color;\n      }\n    }\n\n    &__edit_buttons {\n      @include flexbox(space-between, center);\n\n      button:first-child {\n        @include btn($primary-blue, #fff);\n        padding: 0.3rem 1rem;\n        border: 1px solid $lighten-blue;\n        transition: all 0.3s ease;\n\n        &:hover {\n          border: 1px solid $primary-dark-blue;\n          color: $primary-dark-blue;\n        }\n      }\n\n      button:last-child {\n        @include btn($error-red, #fff);\n        border-radius: 50%;\n        transition: all 0.3s ease;\n        padding: 0.5rem;\n\n        i {\n          @include flexbox(center, center);\n        }\n\n        &:hover {\n          background-color: color.adjust($error-red, $lightness: 40%, $space: hsl);\n        }\n      }\n    }\n  }\n}\n\n@media (width > $mobile) {\n  .addresses {\n    width: 100%;\n    max-width: 600px;\n    @include flexbox(space-between, flex-start);\n    flex-wrap: wrap;\n\n    .addresses__list {\n      width: 300px;\n    }\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAIA;;;;;;AAMA;;;;AAGE;;;;;;AAKE;;;;AAGE;;;;;;;AAOA;;;;AAKF;;;;;;;AAGE;;;;;;;;;;;;;AAME;;;;;AAMF;;;;;;;;;;;;AAME;;;;;;;AAIA;;;;AAQR;EACE;;;;;;;;;EAME", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/utils/modal/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/modal/Modal.module.scss", "turbopack:///turbopack:///[project]/src/components/utils/modal/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/utils/modal/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../scss/variables' as *;\n@use '../../../scss/mixins' as *;\n@use 'sass:color';\n\n.modal {\n  margin: 2rem 1rem 0 1rem;\n  background-color: #fff;\n  border-radius: $border-radius-2;\n  // width: 100%;\n  // max-width: 800px;\n  padding: $padding-5;\n  position: relative;\n  align-self: flex-start;\n\n  .modal_close {\n    @include flexbox(center, center);\n    position: absolute;\n    background-color: #fff;\n    border-radius: 50%;\n    width: 30px;\n    height: 30px;\n    top: 0px !important;\n    right: -35px;\n    font-size: 24px;\n    margin: 0 0 0 0;\n    transition: all 0.2s ease;\n\n    &:hover {\n      background-color: $lighten-blue;\n    }\n\n    i {\n      @include flexbox(center, center);\n      background-color: none;\n      color: black;\n    }\n  }\n}\n\n.modal_body {\n  text-align: center;\n\n  &__title {\n    font-size: $font-size-4;\n    font-weight: bold;\n  }\n\n  &__message {\n    margin: 10px 0;\n    font-size: $font-size-3;\n  }\n\n  .modal_actions {\n    @include flexbox(space-around, center);\n    margin-top: 20px;\n    column-gap: 10px;\n    // background-color: #bda2a2;\n\n    .btn_confirm {\n      @include btn(#fff, $primary-blue);\n      // width: fit-content;\n      padding: 0.36rem 1.2rem;\n      letter-spacing: 0.7px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: color.adjust($lighten-blue, $lightness: -15%, $space: hsl);\n      }\n    }\n\n    .empty_btn_XX {\n      @include btn($lighten-blue, #fff);\n      // margin: 0 auto 0 auto;\n      padding: 0.36rem 1.2rem;\n      border: 1px solid $lighten-blue;\n      letter-spacing: 0.7px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border: 1px solid $primary-blue;\n        color: red;\n      }\n    }\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAIA;;;;;;;;;AAUE;;;;;;;;;;;;;;;;;AAaE;;;;AAIA;;;;;;;;;AAQJ;;;;AAGE;;;;;AAKA;;;;;AAKA;;;;;;;;;AAME;;;;;;;;;;;;;AAOE;;;;AAKF;;;;;;;;;;;;;;AAQE", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/account/change-customer-data/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/account/change-customer-data/DateOfBirthModal.module.scss", "turbopack:///turbopack:///[project]/src/components/account/change-customer-data/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/account/change-customer-data/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../scss/variables' as * ;\n@use '../../../scss/mixins' as * ;\n\n.modal_content {\n  background-color: #fff;\n  padding: 2rem;\n  border-radius: $border-radius-2;\n  max-width: 500px;\n  width: 100%;\n\n  h3 {\n    font-size: $font-size-4;\n    font-weight: bold;\n    margin-bottom: 1rem;\n    text-align: center;\n  }\n\n  form {\n    @include flexbox(flex-start, stretch, column);\n    width: 100%;\n\n    > div:not(.phoneInputContainer) {\n      margin-bottom: 1rem;\n\n      label {\n        font-weight: bold;\n        color: $primary-dark-text-color;\n        display: block;\n        margin-bottom: 0.5rem;\n      }\n\n      input {\n        width: 100%;\n        padding: 0.5rem;\n        border: 1px solid $primary-lighter-text-color;\n        border-radius: 3px;\n\n        &:focus {\n          outline: 2px solid $lighten-blue;\n          border: none;\n        }\n      }\n\n      p {\n        color: $error-red;\n        font-size: 14px;\n        margin-top: 0.5rem;\n        text-align: center;\n      }\n    }\n  }\n}\n\n.modal_buttons {\n  @include flexbox(space-between, center);\n  margin-top: 1rem;\n\n  button {\n    @include btn($primary-blue, #fff);\n    padding: .5rem 1rem;\n    border: 1px solid $lighten-blue;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border: 1px solid $primary-dark-blue;\n      color: $primary-dark-blue;\n    }\n  }\n}\n\n.phoneInputContainer {\n  @include flexbox(flex-start, center);\n  /* Ensure the flag and input are vertically aligned */\n}\n\n.phoneInputFlag {\n  @include flexbox(center, center);\n  height: 100%;\n  /* Ensure the flag dropdown takes full height of the input */\n}\n\n.phoneInputField {\n  width: 100%;\n  padding: 10px;\n  font-size: 16px;\n  box-sizing: border-box;\n  /* Ensures padding doesn't affect width */\n}", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;AAOE;;;;;;;AAOA;;;;;;;;AAIE;;;;AAGE;;;;;;;AAOA;;;;;;;AAME;;;;;AAMF;;;;;;;AAUN;;;;;;;;AAIE;;;;;;;;;;;;;AAME;;;;;AAOJ;;;;;;;AAKA;;;;;;;;AAMA", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/components/account/change-customer-data/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/account/change-customer-data/NameModal.module.scss", "turbopack:///turbopack:///[project]/src/components/account/change-customer-data/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/src/components/account/change-customer-data/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../scss/variables' as * ;\n@use '../../../scss/mixins' as * ;\n\n.modal_content {\n  background-color: #fff;\n  padding: 2rem;\n  border-radius: $border-radius-2;\n  max-width: 500px;\n  width: 100%;\n\n  h3 {\n    font-size: $font-size-4;\n    font-weight: bold;\n    margin-bottom: 1rem;\n    text-align: center;\n  }\n\n  form {\n    @include flexbox(flex-start, stretch, column);\n    width: 100%;\n\n    > div:not(.phoneInputContainer) {\n      margin-bottom: 1rem;\n\n      label {\n        font-weight: bold;\n        color: $primary-dark-text-color;\n        display: block;\n        margin-bottom: 0.5rem;\n      }\n\n      input {\n        width: 100%;\n        padding: 0.5rem;\n        border: 1px solid $primary-lighter-text-color;\n        border-radius: 3px;\n\n        &:focus {\n          outline: 2px solid $lighten-blue;\n          border: none;\n        }\n      }\n\n      p {\n        color: $error-red;\n        font-size: 14px;\n        margin-top: 0.5rem;\n        text-align: center;\n      }\n    }\n  }\n}\n\n.modal_buttons {\n  @include flexbox(space-between, center);\n  margin-top: 1rem;\n\n  button {\n    @include btn($primary-blue, #fff);\n    padding: .5rem 1rem;\n    border: 1px solid $lighten-blue;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border: 1px solid $primary-dark-blue;\n      color: $primary-dark-blue;\n    }\n  }\n}\n\n.phoneInputContainer {\n  @include flexbox(flex-start, center);\n  /* Ensure the flag and input are vertically aligned */\n}\n\n.phoneInputFlag {\n  @include flexbox(center, center);\n  height: 100%;\n  /* Ensure the flag dropdown takes full height of the input */\n}\n\n.phoneInputField {\n  width: 100%;\n  padding: 10px;\n  font-size: 16px;\n  box-sizing: border-box;\n  /* Ensures padding doesn't affect width */\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;AAOE;;;;;;;AAOA;;;;;;;;AAIE;;;;AAGE;;;;;;;AAOA;;;;;;;AAME;;;;;AAMF;;;;;;;AAUN;;;;;;;;AAIE;;;;;;;;;;;;;AAME;;;;;AAOJ;;;;;;;AAKA;;;;;;;;AAMA", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(account)/account/profile/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(account)/account/profile/Profile.module.scss", "turbopack:///turbopack:///[project]/app/(account)/account/profile/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss", "turbopack:///turbopack:///[project]/app/(account)/account/profile/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss"], "sourcesContent": ["@use '../../../../src/scss/variables' as *;\r\n@use '../../../../src/scss/mixins' as *;\r\n\r\n// Mobile-first base styles\r\n.profile {\r\n  width: 100%;\r\n  @include flexbox(flex-start, center, column);\r\n  padding: 0 1rem;\r\n\r\n  .user_details {\r\n    width: 100%;\r\n    max-width: 100%;\r\n  }\r\n}\r\n\r\n.profile__details {\r\n  @include flexbox(flex-start, flex-start, column);\r\n  gap: 1rem;\r\n  padding: 0.5rem;\r\n\r\n  div {\r\n    h5 {\r\n      font-weight: bold;\r\n      color: $primary-dark-text-color;\r\n      margin-bottom: 0.5rem;\r\n      font-size: 16px;\r\n    }\r\n\r\n    p {\r\n      color: $primary-lighter-text-color;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  button {\r\n    align-self: stretch;\r\n    margin-top: 1rem;\r\n  }\r\n}\r\n\r\n.profile__editable {\r\n  padding: 0.5rem;\r\n\r\n  form {\r\n    @include flexbox(flex-start, flex-start, column);\r\n    gap: 1rem;\r\n\r\n    div {\r\n      width: 100%;\r\n\r\n      label {\r\n        font-size: 14px;\r\n      }\r\n\r\n      input {\r\n        width: 100%;\r\n        padding: 0.7rem;\r\n      }\r\n    }\r\n\r\n    button {\r\n      width: 100%;\r\n      margin-top: 1rem;\r\n    }\r\n  }\r\n}\r\n\r\n.edit_btn {\r\n  @include btn($primary-blue, #fff);\r\n  padding: 0.3rem 1.2rem;\r\n  border: 1px solid $lighten-blue;\r\n  letter-spacing: 0.7px;\r\n  transition: all 0.3s ease;\r\n  height: fit-content;\r\n  font-size: 14px;\r\n\r\n  &:hover {\r\n    border: 1px solid $primary-dark-blue;\r\n    color: $primary-dark-blue;\r\n  }\r\n}\r\n\r\n.edit_btn__empty {\r\n  @include btn($lighten-blue, #fff);\r\n  padding: 0.3rem 1.2rem;\r\n  border: 1px solid $lighten-blue;\r\n  letter-spacing: 0.7px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border: 1px solid $primary-blue;\r\n    color: $primary-blue;\r\n  }\r\n}\r\n\r\n.reset_details {\r\n  row-gap: 1.5rem;\r\n  padding: 0.5rem;\r\n\r\n  .reset_details__section {\r\n    @include flexbox(flex-start, flex-start, column);\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n    background-color: #f9f9f9;\r\n    border-radius: 8px;\r\n\r\n    div {\r\n      width: 100%;\r\n\r\n      h5 {\r\n        font-weight: bold;\r\n        color: $primary-dark-text-color;\r\n        margin-bottom: 0.5rem;\r\n        font-size: 16px;\r\n      }\r\n\r\n      p {\r\n        color: $primary-lighter-text-color;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .no_data {\r\n      font-style: italic;\r\n    }\r\n\r\n    button {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n// Tablet and larger screens\r\n@media (min-width: $tablet) {\r\n  .profile {\r\n    padding: 0;\r\n\r\n    .user_details {\r\n      max-width: 600px;\r\n    }\r\n\r\n    h2 {\r\n      padding: 0.5rem 0;\r\n      font-size: 23px;\r\n    }\r\n  }\r\n\r\n  .profile__details {\r\n    @include flexbox(space-between, flex-start, row);\r\n    gap: 2rem;\r\n    padding: 1rem;\r\n\r\n    div {\r\n      h5 {\r\n        font-size: 17px;\r\n      }\r\n\r\n      p {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n\r\n    button {\r\n      align-self: center;\r\n      margin-top: 0;\r\n      max-width: fit-content;\r\n    }\r\n  }\r\n\r\n  .profile__editable {\r\n    padding: 1rem;\r\n\r\n    form {\r\n      @include flexbox(space-between, flex-start, row);\r\n      gap: 2rem;\r\n\r\n      div {\r\n        width: auto;\r\n\r\n        label {\r\n          font-size: 16px;\r\n        }\r\n\r\n        input {\r\n          width: auto;\r\n          padding: 0.5rem;\r\n        }\r\n      }\r\n\r\n      button {\r\n        width: auto;\r\n        margin-top: 0;\r\n        max-width: fit-content;\r\n        align-self: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  .reset_details {\r\n    row-gap: 1rem;\r\n    padding: 1rem;\r\n\r\n    .reset_details__section {\r\n      @include flexbox(space-between, center, row);\r\n      gap: 2rem;\r\n      padding: 0;\r\n      background-color: transparent;\r\n      border-radius: 0;\r\n\r\n      div {\r\n        width: auto;\r\n\r\n        h5 {\r\n          font-size: 17px;\r\n        }\r\n\r\n        p {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      button {\r\n        width: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "@use './variables' as *;\n@use 'sass:color';\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: color.adjust($base-color, $lightness: -($i * 10%), $space: hsl);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$alice-blue: #f0f8ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n"], "names": [], "mappings": "AAIA;;;;;;;;;AAKE;;;;;AAMF;;;;;;;;;AAMI;;;;;;;AAOA;;;;;AAMF;;;;;AAMF;;;;AAGE;;;;;;;;AAIE;;;;AAGE;;;;AAIA;;;;;AAMF;;;;;AAOJ;;;;;;;;;;;;;;;;;AASE;;;;;AAMF;;;;;;;;;;;;;;AAOE;;;;;AAMF;;;;;AAIE;;;;;;;;;;;AAOE;;;;AAGE;;;;;;;AAOA;;;;;AAMF;;;;AAIA;;;;AAOJ;EACE;;;;EAGE;;;;EAIA;;;;;EAMF;;;;;;;;;EAMI;;;;EAIA;;;;EAKF;;;;;;;EAOF;;;;EAGE;;;;;;;;EAIE;;;;EAGE;;;;EAIA;;;;;EAMF;;;;;;;;EASJ;;;;;EAIE;;;;;;;;;;;EAOE;;;;EAGE;;;;EAIA;;;;EAKF", "debugId": null}}]}