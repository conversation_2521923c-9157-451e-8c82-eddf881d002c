{"kind": "FETCH", "data": {"headers": {"allow": "GET", "content-language": "en", "content-length": "2513", "content-type": "application/json", "cross-origin-opener-policy": "same-origin", "date": "<PERSON><PERSON>, 23 Sep 2025 10:54:06 GMT", "djdt-store-id": "a83958b250a940f89921052769964634", "referrer-policy": "same-origin", "server": "WSGIServer/0.2 CPython/3.13.5", "server-timing": "TimerPanel_utime;dur=0;desc=\"User CPU time\", TimerPanel_stime;dur=0;desc=\"System CPU time\", TimerPanel_total;dur=0;desc=\"Total CPU time\", TimerPanel_total_time;dur=5421.734899995499;desc=\"Elapsed time\", SQLPanel_sql_time;dur=3841.163499993854;desc=\"SQL 17 queries\", CachePanel_total_time;dur=0;desc=\"Cache 0 Calls\"", "vary": "Accept, Accept-Language, <PERSON><PERSON>, origin", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "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", "status": 200, "url": "http://localhost:8000/api/products/western-digital-1tb-2tb-8tb-wd-blue-pc-internal-hard-drive/"}, "revalidate": 3600, "tags": []}